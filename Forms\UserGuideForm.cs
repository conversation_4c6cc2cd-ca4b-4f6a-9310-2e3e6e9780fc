using System;
using System.Drawing;
using System.Windows.Forms;

namespace RestaurantManagement.Forms
{
    public partial class UserGuideForm : Form
    {
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        private TreeView topicsTree;
        private RichTextBox contentRichTextBox;

        public UserGuideForm()
        {
            InitializeComponent();
            LoadGuideContent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "دليل المستخدم - نظام إدارة المطاعم";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = LightGray;

            // Header panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = PrimaryColor
            };

            var titleLabel = new Label
            {
                Text = "📖 دليل المستخدم",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "تعلم كيفية استخدام جميع ميزات النظام",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.FromArgb(200, 255, 255, 255),
                Location = new Point(20, 50),
                AutoSize = true
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });

            // Main content panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = LightGray
            };

            // Splitter container
            var splitter = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 250,
                BackColor = Color.White
            };

            // Left panel - Topics tree
            var leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };

            var topicsLabel = new Label
            {
                Text = "📋 المواضيع",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleLeft
            };

            topicsTree = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11F),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                ForeColor = DarkGray,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HideSelection = false
            };
            topicsTree.AfterSelect += TopicsTree_AfterSelect;

            leftPanel.Controls.AddRange(new Control[] { topicsLabel, topicsTree });

            // Right panel - Content
            var rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            var contentLabel = new Label
            {
                Text = "📄 المحتوى",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleLeft
            };

            contentRichTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11F),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                ForeColor = DarkGray,
                ReadOnly = true,
                ScrollBars = RichTextBoxScrollBars.Vertical
            };

            rightPanel.Controls.AddRange(new Control[] { contentLabel, contentRichTextBox });

            // Add panels to splitter
            splitter.Panel1.Controls.Add(leftPanel);
            splitter.Panel2.Controls.Add(rightPanel);

            mainPanel.Controls.Add(splitter);

            // Footer panel
            var footerPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = LightGray,
                Padding = new Padding(20)
            };

            var closeButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = PrimaryColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(100, 35),
                Location = new Point(20, 12),
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();

            var printButton = new Button
            {
                Text = "🖨️ طباعة",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = AccentColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(100, 35),
                Location = new Point(130, 12),
                Cursor = Cursors.Hand
            };
            printButton.FlatAppearance.BorderSize = 0;
            printButton.Click += PrintButton_Click;

            footerPanel.Controls.AddRange(new Control[] { closeButton, printButton });

            // Add all panels to form
            this.Controls.AddRange(new Control[] { headerPanel, mainPanel, footerPanel });
            
            this.ResumeLayout(false);
        }

        private void LoadGuideContent()
        {
            // Clear existing nodes
            topicsTree.Nodes.Clear();

            // Getting Started
            var gettingStarted = new TreeNode("🚀 البدء السريع");
            gettingStarted.Nodes.Add("تسجيل الدخول");
            gettingStarted.Nodes.Add("واجهة البرنامج");
            gettingStarted.Nodes.Add("الإعدادات الأساسية");

            // Orders Management
            var ordersManagement = new TreeNode("🛎️ إدارة الطلبات");
            ordersManagement.Nodes.Add("إنشاء طلب جديد");
            ordersManagement.Nodes.Add("عرض الطلبات");
            ordersManagement.Nodes.Add("تحديث حالة الطلب");
            ordersManagement.Nodes.Add("إلغاء الطلبات");

            // Menu Management
            var menuManagement = new TreeNode("📋 إدارة قائمة الطعام");
            menuManagement.Nodes.Add("إضافة صنف جديد");
            menuManagement.Nodes.Add("تعديل الأصناف");
            menuManagement.Nodes.Add("إدارة الفئات");
            menuManagement.Nodes.Add("تحديد الأسعار");

            // Tables Management
            var tablesManagement = new TreeNode("🪑 إدارة الطاولات");
            tablesManagement.Nodes.Add("عرض حالة الطاولات");
            tablesManagement.Nodes.Add("حجز الطاولات");
            tablesManagement.Nodes.Add("تنظيف الطاولات");

            // Inventory Management
            var inventoryManagement = new TreeNode("📦 إدارة المخزون");
            inventoryManagement.Nodes.Add("عرض المخزون");
            inventoryManagement.Nodes.Add("إضافة مواد جديدة");
            inventoryManagement.Nodes.Add("تحديث الكميات");
            inventoryManagement.Nodes.Add("تنبيهات المخزون");

            // Reports
            var reports = new TreeNode("📊 التقارير");
            reports.Nodes.Add("تقرير المبيعات");
            reports.Nodes.Add("تقرير الأرباح");
            reports.Nodes.Add("تقرير المخزون");
            reports.Nodes.Add("أداء الموظفين");

            // Troubleshooting
            var troubleshooting = new TreeNode("🔧 حل المشاكل");
            troubleshooting.Nodes.Add("مشاكل الاتصال");
            troubleshooting.Nodes.Add("مشاكل الطباعة");
            troubleshooting.Nodes.Add("مشاكل البيانات");

            // Add all nodes to tree
            topicsTree.Nodes.AddRange(new TreeNode[] {
                gettingStarted, ordersManagement, menuManagement, 
                tablesManagement, inventoryManagement, reports, troubleshooting
            });

            // Expand all nodes
            topicsTree.ExpandAll();

            // Select first node
            if (topicsTree.Nodes.Count > 0)
            {
                topicsTree.SelectedNode = gettingStarted.Nodes[0];
                ShowContent("تسجيل الدخول");
            }
        }

        private void TopicsTree_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node != null && e.Node.Level > 0) // Only leaf nodes
            {
                ShowContent(e.Node.Text);
            }
        }

        private void ShowContent(string topic)
        {
            contentRichTextBox.Clear();
            
            // Set RTF formatting
            contentRichTextBox.SelectionFont = new Font("Segoe UI", 14F, FontStyle.Bold);
            contentRichTextBox.SelectionColor = DarkGray;
            contentRichTextBox.AppendText($"{topic}\n\n");

            contentRichTextBox.SelectionFont = new Font("Segoe UI", 11F);
            contentRichTextBox.SelectionColor = Color.FromArgb(80, 80, 80);

            switch (topic)
            {
                case "تسجيل الدخول":
                    contentRichTextBox.AppendText(
                        "خطوات تسجيل الدخول:\n\n" +
                        "1. اختر الفرع من القائمة المنسدلة\n" +
                        "2. أدخل اسم المستخدم (افتراضي: admin)\n" +
                        "3. أدخل كلمة المرور (افتراضي: admin123)\n" +
                        "4. اضغط زر 'دخول'\n\n" +
                        "ملاحظة: يمكن تغيير كلمة المرور من الإعدادات بعد تسجيل الدخول."
                    );
                    break;

                case "واجهة البرنامج":
                    contentRichTextBox.AppendText(
                        "مكونات الواجهة الرئيسية:\n\n" +
                        "• شريط القوائم: يحتوي على جميع الوظائف الرئيسية\n" +
                        "• شريط الأدوات: أزرار سريعة للوظائف المهمة\n" +
                        "• المنطقة الرئيسية: عرض البيانات والنماذج\n" +
                        "• شريط الحالة: معلومات المستخدم والفرع الحالي\n\n" +
                        "يمكن الوصول لجميع الوظائف من شريط القوائم أو الأزرار السريعة."
                    );
                    break;

                case "إنشاء طلب جديد":
                    contentRichTextBox.AppendText(
                        "خطوات إنشاء طلب جديد:\n\n" +
                        "1. اضغط 'طلب جديد' من القائمة أو شريط الأدوات\n" +
                        "2. اختر نوع الطلب (تناول في المطعم، تكاوي، دليفري)\n" +
                        "3. حدد الطاولة (للطلبات الداخلية)\n" +
                        "4. أدخل بيانات العميل\n" +
                        "5. اختر الأصناف من قائمة الطعام\n" +
                        "6. راجع المجموع النهائي\n" +
                        "7. اضغط 'تأكيد الطلب'\n\n" +
                        "سيتم حفظ الطلب وطباعة الفاتورة تلقائياً."
                    );
                    break;

                case "عرض المخزون":
                    contentRichTextBox.AppendText(
                        "عرض وإدارة المخزون:\n\n" +
                        "• عرض جميع المواد مع الكميات الحالية\n" +
                        "• تصفية المواد حسب الفئة\n" +
                        "• البحث في المواد بالاسم\n" +
                        "• عرض المواد منخفضة المخزون\n" +
                        "• عرض المواد منتهية الصلاحية\n\n" +
                        "الألوان المستخدمة:\n" +
                        "• أحمر: مخزون منخفض\n" +
                        "• أصفر: منتهي الصلاحية قريباً\n" +
                        "• أخضر: مخزون طبيعي"
                    );
                    break;

                default:
                    contentRichTextBox.AppendText(
                        "سيتم إضافة محتوى مفصل لهذا الموضوع قريباً.\n\n" +
                        "للحصول على مساعدة فورية، يرجى الاتصال بالدعم الفني."
                    );
                    break;
            }
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Simple print functionality
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "قيد التطوير", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
