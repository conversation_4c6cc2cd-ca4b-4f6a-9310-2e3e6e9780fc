using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Models;

namespace RestaurantManagement.Data
{
    public static class DatabaseInitializer
    {
        public static void InitializeDatabase()
        {
            try
            {
                CreateDatabaseIfNotExists();
                CreateTablesIfNotExist();
                InsertInitialData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        public static void ResetMenuItems()
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // حذف الأصناف الموجودة
                string deleteMenuItems = "DELETE FROM MenuItems";
                ExecuteCommand(connection, deleteMenuItems);

                // إعادة إدراج الأصناف بالترميز الصحيح
                InsertMenuItemsWithParameters(connection);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين قائمة الطعام: {ex.Message}");
                throw;
            }
        }

        public static void ResetAllData()
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // حذف البيانات بالترتيب الصحيح (بسبب Foreign Keys)
                ExecuteCommand(connection, "DELETE FROM OrderItems");
                ExecuteCommand(connection, "DELETE FROM Orders");
                ExecuteCommand(connection, "DELETE FROM MenuItems");
                ExecuteCommand(connection, "DELETE FROM Employees");
                ExecuteCommand(connection, "DELETE FROM Branches");

                // إعادة تعيين Identity
                ExecuteCommand(connection, "DBCC CHECKIDENT ('Branches', RESEED, 0)");
                ExecuteCommand(connection, "DBCC CHECKIDENT ('Employees', RESEED, 0)");
                ExecuteCommand(connection, "DBCC CHECKIDENT ('MenuItems', RESEED, 0)");
                ExecuteCommand(connection, "DBCC CHECKIDENT ('Orders', RESEED, 0)");
                ExecuteCommand(connection, "DBCC CHECKIDENT ('OrderItems', RESEED, 0)");

                // إعادة إدراج البيانات بالترميز الصحيح
                InsertBranchesWithParameters(connection);
                InsertEmployeesWithParameters(connection);
                InsertMenuItemsWithParameters(connection);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين جميع البيانات: {ex.Message}");
                throw;
            }
        }

        private static void CreateDatabaseIfNotExists()
        {
            string masterConnectionString = "Server=(localdb)\\MSSQLLocalDB;Integrated Security=true;TrustServerCertificate=true;";
            
            using var connection = new SqlConnection(masterConnectionString);
            connection.Open();
            
            string checkDbQuery = "SELECT COUNT(*) FROM sys.databases WHERE name = 'RestaurantManagement'";
            using var checkCommand = new SqlCommand(checkDbQuery, connection);
            int dbExists = (int)checkCommand.ExecuteScalar();
            
            if (dbExists == 0)
            {
                string createDbQuery = @"
                    CREATE DATABASE RestaurantManagement
                    COLLATE Arabic_CI_AS";
                
                using var createCommand = new SqlCommand(createDbQuery, connection);
                createCommand.ExecuteNonQuery();
            }
        }

        private static void CreateTablesIfNotExist()
        {
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            // Create Branches table
            string createBranchesTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
                CREATE TABLE Branches (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Address NVARCHAR(200),
                    Phone NVARCHAR(20),
                    Email NVARCHAR(100),
                    IsActive BIT DEFAULT 1,
                    CreatedDate DATETIME DEFAULT GETDATE()
                )";
            
            ExecuteCommand(connection, createBranchesTable);

            // Create Employees table
            string createEmployeesTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
                CREATE TABLE Employees (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    Name NVARCHAR(100) NOT NULL,
                    Username NVARCHAR(50) UNIQUE NOT NULL,
                    Password NVARCHAR(100) NOT NULL,
                    Role INT NOT NULL,
                    Phone NVARCHAR(20),
                    Email NVARCHAR(100),
                    IsActive BIT DEFAULT 1,
                    CreatedDate DATETIME DEFAULT GETDATE(),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id)
                )";
            
            ExecuteCommand(connection, createEmployeesTable);

            // Create MenuItems table
            string createMenuItemsTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MenuItems' AND xtype='U')
                CREATE TABLE MenuItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(500),
                    Price DECIMAL(10,2) NOT NULL,
                    Category NVARCHAR(50) NOT NULL,
                    IsAvailable BIT DEFAULT 1,
                    CreatedDate DATETIME DEFAULT GETDATE()
                )";
            
            ExecuteCommand(connection, createMenuItemsTable);

            // Create Orders table
            string createOrdersTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Orders' AND xtype='U')
                CREATE TABLE Orders (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    TableNumber INT NULL,
                    CustomerId INT NULL,
                    CustomerName NVARCHAR(100),
                    CustomerPhone NVARCHAR(20),
                    CustomerEmail NVARCHAR(100),
                    CustomerAddress NVARCHAR(200),
                    Type INT NOT NULL,
                    Status INT NOT NULL,
                    OrderDate DATETIME DEFAULT GETDATE(),
                    SubTotal DECIMAL(10,2) NOT NULL,
                    Tax DECIMAL(10,2) DEFAULT 0,
                    ServiceCharge DECIMAL(10,2) DEFAULT 0,
                    DeliveryFee DECIMAL(10,2) DEFAULT 0,
                    Discount DECIMAL(10,2) DEFAULT 0,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    Notes NVARCHAR(500),
                    EmployeeId INT NOT NULL,
                    PaymentMethod NVARCHAR(50) DEFAULT N'نقدي',
                    IsPaid BIT DEFAULT 0,
                    DeliveryTime DATETIME NULL,
                    DeliveryDriverName NVARCHAR(100),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                )";
            
            ExecuteCommand(connection, createOrdersTable);

            // Create OrderItems table
            string createOrderItemsTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U')
                CREATE TABLE OrderItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    OrderId INT NOT NULL,
                    MenuItemId INT NOT NULL,
                    MenuItemName NVARCHAR(100) NOT NULL,
                    Price DECIMAL(10,2) NOT NULL,
                    Quantity INT NOT NULL,
                    Notes NVARCHAR(200),
                    FOREIGN KEY (OrderId) REFERENCES Orders(Id) ON DELETE CASCADE,
                    FOREIGN KEY (MenuItemId) REFERENCES MenuItems(Id)
                )";
            
            ExecuteCommand(connection, createOrderItemsTable);
        }

        private static void InsertInitialData()
        {
            using var connection = DatabaseHelper.GetConnection();
            connection.Open();

            // Insert Branches with proper Arabic encoding
            string checkBranches = "SELECT COUNT(*) FROM Branches";
            using var checkBranchesCmd = new SqlCommand(checkBranches, connection);
            int branchCount = (int)checkBranchesCmd.ExecuteScalar();

            if (branchCount == 0)
            {
                InsertBranchesWithParameters(connection);
            }

            // Insert Employees with proper Arabic encoding
            string checkEmployees = "SELECT COUNT(*) FROM Employees";
            using var checkEmployeesCmd = new SqlCommand(checkEmployees, connection);
            int employeeCount = (int)checkEmployeesCmd.ExecuteScalar();

            if (employeeCount == 0)
            {
                InsertEmployeesWithParameters(connection);
            }

            // Insert MenuItems with proper Arabic encoding
            string checkMenuItems = "SELECT COUNT(*) FROM MenuItems";
            using var checkMenuItemsCmd = new SqlCommand(checkMenuItems, connection);
            int menuItemCount = (int)checkMenuItemsCmd.ExecuteScalar();

            if (menuItemCount == 0)
            {
                InsertMenuItemsWithParameters(connection);
            }
        }

        private static void InsertBranchesWithParameters(SqlConnection connection)
        {
            var branches = new[]
            {
                new { Name = "الفرع الرئيسي", Address = "شارع التحرير، وسط البلد", Phone = "02-12345678", Email = "<EMAIL>" },
                new { Name = "فرع المعادي", Address = "شارع 9، المعادي", Phone = "02-87654321", Email = "<EMAIL>" },
                new { Name = "فرع مدينة نصر", Address = "شارع عباس العقاد، مدينة نصر", Phone = "02-11223344", Email = "<EMAIL>" },
                new { Name = "فرع الزمالك", Address = "شارع 26 يوليو، الزمالك", Phone = "02-55667788", Email = "<EMAIL>" },
                new { Name = "فرع الإسكندرية", Address = "كورنيش الإسكندرية، سيدي جابر", Phone = "03-99887766", Email = "<EMAIL>" }
            };

            string insertQuery = @"INSERT INTO Branches (Name, Address, Phone, Email, IsActive, CreatedDate)
                                 VALUES (@Name, @Address, @Phone, @Email, 1, GETDATE())";

            foreach (var branch in branches)
            {
                using var command = new SqlCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@Name", branch.Name);
                command.Parameters.AddWithValue("@Address", branch.Address);
                command.Parameters.AddWithValue("@Phone", branch.Phone);
                command.Parameters.AddWithValue("@Email", branch.Email);
                command.ExecuteNonQuery();
            }
        }

        private static void InsertEmployeesWithParameters(SqlConnection connection)
        {
            var employees = new[]
            {
                new { BranchId = 1, Name = "مدير النظام", Username = "admin", Password = "admin123", Role = 0, Phone = "01000000001", Email = "<EMAIL>" },
                new { BranchId = 1, Name = "أحمد محمد", Username = "ahmed", Password = "pass123", Role = 1, Phone = "01000000002", Email = "<EMAIL>" },
                new { BranchId = 1, Name = "فاطمة علي", Username = "fatma", Password = "pass123", Role = 2, Phone = "01000000003", Email = "<EMAIL>" },
                new { BranchId = 2, Name = "محمد حسن", Username = "mohamed", Password = "pass123", Role = 1, Phone = "01000000004", Email = "<EMAIL>" },
                new { BranchId = 3, Name = "سارة أحمد", Username = "sara", Password = "pass123", Role = 1, Phone = "01000000005", Email = "<EMAIL>" },
                new { BranchId = 1, Name = "علي حسام", Username = "ali", Password = "pass123", Role = 2, Phone = "01000000006", Email = "<EMAIL>" },
                new { BranchId = 2, Name = "نورا سمير", Username = "nora", Password = "pass123", Role = 2, Phone = "01000000007", Email = "<EMAIL>" }
            };

            string insertQuery = @"INSERT INTO Employees (BranchId, Name, Username, Password, Role, Phone, Email, IsActive, CreatedDate)
                                 VALUES (@BranchId, @Name, @Username, @Password, @Role, @Phone, @Email, 1, GETDATE())";

            foreach (var employee in employees)
            {
                using var command = new SqlCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@BranchId", employee.BranchId);
                command.Parameters.AddWithValue("@Name", employee.Name);
                command.Parameters.AddWithValue("@Username", employee.Username);
                command.Parameters.AddWithValue("@Password", employee.Password);
                command.Parameters.AddWithValue("@Role", employee.Role);
                command.Parameters.AddWithValue("@Phone", employee.Phone);
                command.Parameters.AddWithValue("@Email", employee.Email);
                command.ExecuteNonQuery();
            }
        }

        private static void InsertMenuItemsWithParameters(SqlConnection connection)
        {
            var menuItems = new[]
            {
                new { Name = "برجر لحم", Description = "برجر لحم مشوي مع الخضار والصوص الخاص", Price = 45.00m, Category = "برجر" },
                new { Name = "برجر دجاج", Description = "برجر دجاج مقرمش مع الخس والطماطم", Price = 40.00m, Category = "برجر" },
                new { Name = "بيتزا مارجريتا", Description = "بيتزا بالجبن والطماطم والريحان", Price = 55.00m, Category = "بيتزا" },
                new { Name = "بيتزا بيبروني", Description = "بيتزا بالبيبروني والجبن الموتزاريلا", Price = 65.00m, Category = "بيتزا" },
                new { Name = "سلطة يونانية", Description = "سلطة خضراء مع الجبن الأبيض والزيتون", Price = 25.00m, Category = "سلطات" },
                new { Name = "سلطة سيزر", Description = "سلطة خس مع الدجاج المشوي وصوص السيزر", Price = 35.00m, Category = "سلطات" },
                new { Name = "عصير برتقال طازج", Description = "عصير برتقال طبيعي 100%", Price = 15.00m, Category = "مشروبات" },
                new { Name = "عصير مانجو", Description = "عصير مانجو طازج", Price = 18.00m, Category = "مشروبات" },
                new { Name = "شاي أحمر", Description = "شاي أحمر تقليدي", Price = 8.00m, Category = "مشروبات ساخنة" },
                new { Name = "قهوة تركية", Description = "قهوة تركية أصلية", Price = 12.00m, Category = "مشروبات ساخنة" },
                new { Name = "كنافة بالجبن", Description = "كنافة طازجة بالجبن والقطر", Price = 30.00m, Category = "حلويات" },
                new { Name = "أم علي", Description = "أم علي بالمكسرات والزبيب", Price = 25.00m, Category = "حلويات" },
                new { Name = "كباب مشوي", Description = "كباب لحم مشوي على الفحم", Price = 50.00m, Category = "مشاوي" },
                new { Name = "فراخ مشوية", Description = "نصف فرخة مشوية مع الأرز", Price = 45.00m, Category = "مشاوي" },
                new { Name = "ملوخية باللحمة", Description = "ملوخية خضراء باللحمة والأرز الأبيض", Price = 35.00m, Category = "أطباق رئيسية" },
                new { Name = "مولوخية بالفراخ", Description = "ملوخية خضراء بالفراخ والأرز الأبيض", Price = 32.00m, Category = "أطباق رئيسية" },
                new { Name = "كشري", Description = "كشري مصري أصلي بالشعرية والعدس", Price = 20.00m, Category = "أطباق شعبية" },
                new { Name = "فول وطعمية", Description = "فول مدمس وطعمية مع السلطة والطحينة", Price = 15.00m, Category = "أطباق شعبية" }
            };

            string insertQuery = @"INSERT INTO MenuItems (Name, Description, Price, Category, IsAvailable, CreatedDate)
                                 VALUES (@Name, @Description, @Price, @Category, 1, GETDATE())";

            foreach (var item in menuItems)
            {
                using var command = new SqlCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@Name", item.Name);
                command.Parameters.AddWithValue("@Description", item.Description);
                command.Parameters.AddWithValue("@Price", item.Price);
                command.Parameters.AddWithValue("@Category", item.Category);
                command.ExecuteNonQuery();
            }
        }

        private static void ExecuteCommand(SqlConnection connection, string commandText)
        {
            try
            {
                using var command = new SqlCommand(commandText, connection);
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ الأمر: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"الأمر: {commandText}");
                throw;
            }
        }
    }
}
