# نظام إدارة المطعم - Restaurant Management System

نظام شامل لإدارة المطاعم الكبيرة مع دعم الفروع المتعددة، مطور بلغة C# مع Windows Forms وقاعدة بيانات SQL Server.

## الميزات الرئيسية

### 🏢 إدارة الفروع المتعددة
- إضافة وإدارة فروع متعددة
- إعدادات مخصصة لكل فرع (الضرائب، رسوم التوصيل، إلخ)
- تبديل بين الفروع للمديرين

### 📋 إدارة قائمة الطعام
- إضافة وتعديل وحذف الأصناف
- تصنيف الأصناف حسب الفئات
- إدارة الأسعار والتوفر
- دعم الصور للأصناف

### 🛎️ نظام الطلبات المتقدم
- طلبات تناول في المطعم (Dine-in)
- طلبات تكاوي (Takeaway)
- طلبات دليفري (Delivery)
- تتبع حالة الطلبات
- حساب الضرائب والرسوم تلقائياً

### 🪑 إدارة الطاولات والحجوزات
- إدارة طاولات متعددة (أكثر من 20 طاولة)
- نظام حجز الطاولات المتقدم
- رموز QR للطاولات
- تتبع حالة الطاولات (متاح، مشغول، محجوز، قيد التنظيف)

### 📦 إدارة المخزون الذكية
- تتبع المواد الخام والمخزون
- تنبيهات المخزون المنخفض
- تنبيهات انتهاء الصلاحية
- حركات المخزون (شراء، استخدام، تعديل، نقل بين الفروع)
- تقارير المخزون التفصيلية

### 👥 إدارة الموظفين والمناوبات
- إدارة بيانات الموظفين
- أدوار مختلفة (مدير، كاشير، نادل، طباخ، دليفري)
- تسجيل المناوبات
- تتبع ساعات العمل

### 👤 إدارة العملاء ونقاط الولاء
- قاعدة بيانات العملاء
- نظام نقاط الولاء
- تتبع تاريخ الطلبات
- عملاء VIP

### 📊 التقارير والتحليلات
- تقارير المبيعات اليومية والشهرية
- تقارير الأرباح والخسائر
- تحليل أداء الموظفين
- تقارير المخزون
- تقارير مبيعات الأصناف

## متطلبات النظام

### البرمجيات المطلوبة
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- SQL Server 2019 أو أحدث (أو SQL Server Express)
- Visual Studio 2022 (للتطوير)

### متطلبات الأجهزة
- معالج: Intel Core i3 أو أفضل
- الذاكرة: 4 GB RAM كحد أدنى، 8 GB مُوصى به
- مساحة القرص: 500 MB للتطبيق + مساحة لقاعدة البيانات
- الشاشة: 1366x768 كحد أدنى، 1920x1080 مُوصى به

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات

#### أ. SQL Server المحلي (Integrated Security)
```xml
<connectionStrings>
    <add name="RestaurantDB" 
         connectionString="Server=localhost;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;" 
         providerName="Microsoft.Data.SqlClient" />
</connectionStrings>
```

#### ب. SQL Server مع اسم مستخدم وكلمة مرور
```xml
<connectionStrings>
    <add name="RestaurantDB" 
         connectionString="Server=localhost;Database=RestaurantManagement;User Id=sa;Password=YourPassword;TrustServerCertificate=true;" 
         providerName="Microsoft.Data.SqlClient" />
</connectionStrings>
```

#### ج. SQL Server Express
```xml
<connectionStrings>
    <add name="RestaurantDB" 
         connectionString="Server=.\SQLEXPRESS;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;" 
         providerName="Microsoft.Data.SqlClient" />
</connectionStrings>
```

### 2. تشغيل التطبيق

1. افتح مجلد المشروع في Visual Studio 2022
2. تأكد من تثبيت الحزم المطلوبة:
   ```
   Microsoft.Data.SqlClient (5.1.1)
   System.Configuration.ConfigurationManager (7.0.0)
   ```
3. عدّل سلسلة الاتصال في ملف `App.config` حسب إعداد SQL Server لديك
4. اضغط F5 لتشغيل التطبيق

### 3. البيانات الافتراضية

عند التشغيل الأول، سيتم إنشاء:
- فرع افتراضي: "الفرع الرئيسي"
- مستخدم مدير: `admin` / `admin123`
- 25 طاولة افتراضية
- قائمة طعام نموذجية
- عناصر مخزون أساسية

## كيفية الاستخدام

### تسجيل الدخول
1. اختر الفرع من القائمة المنسدلة
2. أدخل اسم المستخدم: `admin`
3. أدخل كلمة المرور: `admin123`
4. اضغط "دخول"

### الوظائف الأساسية

#### إدارة الطلبات
- **طلب جديد**: إنشاء طلب جديد (تناول في المطعم، تكاوي، أو دليفري)
- **عرض الطلبات**: مراجعة جميع الطلبات
- **حالة الطلبات**: تتبع وتحديث حالة الطلبات

#### إدارة الطاولات
- **عرض الطاولات**: مراقبة حالة جميع الطاولات
- **الحجوزات**: إدارة حجوزات الطاولات

#### إدارة المخزون
- **إدارة المخزون**: إضافة وتعديل عناصر المخزون
- **حركات المخزون**: تسجيل المشتريات والاستخدام
- **تقرير المخزون**: عرض تقارير المخزون والتنبيهات

#### التقارير
- **تقرير المبيعات**: تحليل المبيعات حسب الفترة
- **تقرير الأرباح**: حساب الأرباح والخسائر
- **أداء الموظفين**: تقييم أداء الفريق

## الأدوار والصلاحيات

### مدير (Manager)
- الوصول الكامل لجميع الوظائف
- إدارة الفروع والموظفين
- عرض جميع التقارير
- إعدادات النظام

### كاشير (Cashier)
- إنشاء ومعالجة الطلبات
- إدارة المدفوعات
- عرض تقارير المبيعات الأساسية

### نادل (Waiter)
- إنشاء طلبات تناول في المطعم
- إدارة الطاولات والحجوزات
- تحديث حالة الطلبات

### طباخ (Chef)
- عرض طلبات المطبخ
- تحديث حالة تحضير الطلبات
- إدارة المخزون الأساسية

### دليفري (Delivery)
- عرض طلبات التوصيل
- تحديث حالة التوصيل
- تسجيل أوقات التسليم

## الإعدادات المتقدمة

### إعدادات الفرع
- معدل الضريبة
- رسوم الخدمة
- رسوم التوصيل
- الحد الأدنى لطلبات التوصيل
- قبول الطلبات الإلكترونية
- قبول الحجوزات

### إعدادات التطبيق
```xml
<appSettings>
    <add key="TaxRate" value="0.14" />
    <add key="DeliveryFee" value="10.00" />
    <add key="RestaurantName" value="مطعم الأصالة" />
    <add key="RestaurantPhone" value="01000000000" />
    <add key="RestaurantAddress" value="شارع الجمهورية، القاهرة" />
</appSettings>
```

## الأمان

### كلمات المرور
- يُنصح بتغيير كلمة مرور المدير الافتراضية
- في الإصدارات المستقبلية، سيتم تشفير كلمات المرور

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- احفظ النسخ الاحتياطية في مكان آمن

## الدعم الفني

### المشاكل الشائعة

#### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل SQL Server
2. تحقق من سلسلة الاتصال في `App.config`
3. تأكد من صلاحيات المستخدم

#### التطبيق لا يبدأ
1. تأكد من تثبيت .NET 6.0 Runtime
2. تحقق من وجود جميع الملفات المطلوبة
3. شغّل التطبيق كمدير إذا لزم الأمر

## التطوير المستقبلي

### الميزات المخططة
- [ ] واجهة ويب للطلبات الإلكترونية
- [ ] تطبيق موبايل للعملاء
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع أنظمة المحاسبة
- [ ] دعم متعدد اللغات
- [ ] نظام إدارة الموردين

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

## المساهمة

نرحب بالمساهمات لتحسين النظام. يرجى:
1. إنشاء Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

---

**تم التطوير بواسطة**: فريق تطوير نظم إدارة المطاعم  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2024  
**اللغة**: C# / .NET 6.0  
**قاعدة البيانات**: SQL Server  
**الواجهة**: Windows Forms