using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class BranchManagementForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private BranchService branchService;
        private List<Branch> branches;

        // Controls
        private ListView branchesListView = null!;
        private Button addBranchButton = null!;
        private Button editBranchButton = null!;
        private Button deleteBranchButton = null!;
        private Button refreshButton = null!;
        private Button closeButton = null!;

        // Add/Edit controls
        private Panel editPanel = null!;
        private TextBox nameTextBox = null!;
        private TextBox addressTextBox = null!;
        private TextBox phoneTextBox = null!;
        private TextBox emailTextBox = null!;
        private CheckBox isActiveCheckBox = null!;
        private Button saveButton = null!;
        private Button cancelEditButton = null!;
        private int editingBranchId = 0;

        public BranchManagementForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            branchService = new BranchService();
            branches = new List<Branch>();
            
            InitializeComponent();
            LoadBranches();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "إدارة الفروع - Branch Management";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            // Header
            var headerLabel = new Label
            {
                Text = "إدارة الفروع - Branch Management",
                Location = new Point(20, 20),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.DarkBlue
            };
            this.Controls.Add(headerLabel);

            // Branches list
            branchesListView = new ListView
            {
                Location = new Point(20, 60),
                Size = new Size(850, 250),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false
            };
            branchesListView.Columns.Add("الرقم", 60);
            branchesListView.Columns.Add("اسم الفرع", 200);
            branchesListView.Columns.Add("العنوان", 250);
            branchesListView.Columns.Add("الهاتف", 120);
            branchesListView.Columns.Add("البريد الإلكتروني", 150);
            branchesListView.Columns.Add("نشط", 60);
            this.Controls.Add(branchesListView);

            // Action buttons
            addBranchButton = new Button
            {
                Text = "إضافة فرع جديد",
                Location = new Point(20, 330),
                Size = new Size(120, 35),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addBranchButton.Click += AddBranchButton_Click;
            this.Controls.Add(addBranchButton);

            editBranchButton = new Button
            {
                Text = "تعديل الفرع",
                Location = new Point(150, 330),
                Size = new Size(120, 35),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };
            editBranchButton.Click += EditBranchButton_Click;
            this.Controls.Add(editBranchButton);

            deleteBranchButton = new Button
            {
                Text = "حذف الفرع",
                Location = new Point(280, 330),
                Size = new Size(120, 35),
                BackColor = Color.Red,
                ForeColor = Color.White
            };
            deleteBranchButton.Click += DeleteBranchButton_Click;
            this.Controls.Add(deleteBranchButton);

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(410, 330),
                Size = new Size(80, 35),
                BackColor = Color.Blue,
                ForeColor = Color.White
            };
            refreshButton.Click += RefreshButton_Click;
            this.Controls.Add(refreshButton);

            closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(750, 330),
                Size = new Size(120, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);

            // Edit panel
            CreateEditPanel();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateEditPanel()
        {
            editPanel = new Panel
            {
                Location = new Point(20, 380),
                Size = new Size(850, 180),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            var editLabel = new Label
            {
                Text = "إضافة/تعديل فرع:",
                Location = new Point(10, 10),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            editPanel.Controls.Add(editLabel);

            var nameLabel = new Label
            {
                Text = "اسم الفرع:",
                Location = new Point(10, 40),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(nameLabel);

            nameTextBox = new TextBox
            {
                Location = new Point(100, 40),
                Size = new Size(200, 23)
            };
            editPanel.Controls.Add(nameTextBox);

            var addressLabel = new Label
            {
                Text = "العنوان:",
                Location = new Point(320, 40),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(addressLabel);

            addressTextBox = new TextBox
            {
                Location = new Point(380, 40),
                Size = new Size(300, 23)
            };
            editPanel.Controls.Add(addressTextBox);

            var phoneLabel = new Label
            {
                Text = "الهاتف:",
                Location = new Point(10, 80),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(phoneLabel);

            phoneTextBox = new TextBox
            {
                Location = new Point(70, 80),
                Size = new Size(150, 23)
            };
            editPanel.Controls.Add(phoneTextBox);

            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(240, 80),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(emailLabel);

            emailTextBox = new TextBox
            {
                Location = new Point(350, 80),
                Size = new Size(200, 23)
            };
            editPanel.Controls.Add(emailTextBox);

            isActiveCheckBox = new CheckBox
            {
                Text = "فرع نشط",
                Location = new Point(570, 80),
                Size = new Size(80, 23),
                Checked = true
            };
            editPanel.Controls.Add(isActiveCheckBox);

            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(100, 120),
                Size = new Size(100, 35),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;
            editPanel.Controls.Add(saveButton);

            cancelEditButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(220, 120),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            cancelEditButton.Click += CancelEditButton_Click;
            editPanel.Controls.Add(cancelEditButton);

            this.Controls.Add(editPanel);
        }

        private void LoadBranches()
        {
            try
            {
                branches = branchService.GetAllBranches();
                UpdateBranchesList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateBranchesList()
        {
            branchesListView.Items.Clear();

            foreach (var branch in branches)
            {
                var listItem = new ListViewItem(branch.Id.ToString());
                listItem.SubItems.Add(branch.Name);
                listItem.SubItems.Add(branch.Address ?? "");
                listItem.SubItems.Add(branch.Phone ?? "");
                listItem.SubItems.Add(branch.Email ?? "");
                listItem.SubItems.Add(branch.IsActive ? "نعم" : "لا");
                listItem.Tag = branch;

                if (!branch.IsActive)
                {
                    listItem.BackColor = Color.LightGray;
                    listItem.ForeColor = Color.DarkGray;
                }

                branchesListView.Items.Add(listItem);
            }
        }

        private void AddBranchButton_Click(object? sender, EventArgs e)
        {
            editingBranchId = 0;
            ClearEditForm();
            editPanel.Visible = true;
            nameTextBox.Focus();
        }

        private void EditBranchButton_Click(object? sender, EventArgs e)
        {
            if (branchesListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فرع للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedBranch = (Branch)branchesListView.SelectedItems[0].Tag;
            editingBranchId = selectedBranch.Id;
            
            nameTextBox.Text = selectedBranch.Name;
            addressTextBox.Text = selectedBranch.Address ?? "";
            phoneTextBox.Text = selectedBranch.Phone ?? "";
            emailTextBox.Text = selectedBranch.Email ?? "";
            isActiveCheckBox.Checked = selectedBranch.IsActive;
            
            editPanel.Visible = true;
            nameTextBox.Focus();
        }

        private void DeleteBranchButton_Click(object? sender, EventArgs e)
        {
            if (branchesListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فرع للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedBranch = (Branch)branchesListView.SelectedItems[0].Tag;
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف الفرع '{selectedBranch.Name}'؟", 
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    branchService.DeleteBranch(selectedBranch.Id);
                    MessageBox.Show("تم حذف الفرع بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadBranches();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفرع: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفرع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var branch = new Branch
                {
                    Id = editingBranchId,
                    Name = nameTextBox.Text.Trim(),
                    Address = addressTextBox.Text.Trim(),
                    Phone = phoneTextBox.Text.Trim(),
                    Email = emailTextBox.Text.Trim(),
                    IsActive = isActiveCheckBox.Checked,
                    CreatedDate = editingBranchId == 0 ? DateTime.Now : DateTime.Now // Will be handled by service
                };

                if (editingBranchId == 0)
                {
                    branchService.AddBranch(branch);
                    MessageBox.Show("تم إضافة الفرع بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    branchService.UpdateBranch(branch);
                    MessageBox.Show("تم تحديث الفرع بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                editPanel.Visible = false;
                LoadBranches();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفرع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadBranches();
        }

        private void CancelEditButton_Click(object? sender, EventArgs e)
        {
            editPanel.Visible = false;
            ClearEditForm();
        }

        private void ClearEditForm()
        {
            nameTextBox.Clear();
            addressTextBox.Clear();
            phoneTextBox.Clear();
            emailTextBox.Clear();
            isActiveCheckBox.Checked = true;
        }
    }
}
