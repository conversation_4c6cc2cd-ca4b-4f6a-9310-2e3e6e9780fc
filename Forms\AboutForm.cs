using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace RestaurantManagement.Forms
{
    public partial class AboutForm : Form
    {
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        public AboutForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "حول البرنامج";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = Color.White;

            // Header panel with gradient
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 150,
                BackColor = PrimaryColor
            };
            headerPanel.Paint += HeaderPanel_Paint;

            // Logo
            var logoLabel = new Label
            {
                Text = "🍽️",
                Font = new Font("Segoe UI Emoji", 48F),
                ForeColor = Color.White,
                Location = new Point(275, 30),
                Size = new Size(80, 80),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // App title
            var titleLabel = new Label
            {
                Text = "نظام إدارة المطاعم",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(50, 110),
                Size = new Size(500, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.AddRange(new Control[] { logoLabel, titleLabel });

            // Content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(40, 30, 40, 30),
                BackColor = Color.White
            };

            // Version info
            var versionCard = CreateInfoCard("📋 معلومات الإصدار", 
                "الإصدار: 1.0.0\nتاريخ الإصدار: 2024\nنوع الترخيص: تجاري", 0);

            // Developer info
            var developerCard = CreateInfoCard("👨‍💻 معلومات المطور", 
                "تم التطوير بواسطة: فريق التطوير\nاللغة: C# / .NET 6.0\nقاعدة البيانات: SQL Server\nالواجهة: Windows Forms", 180);

            // Features info
            var featuresCard = CreateInfoCard("⭐ الميزات الرئيسية", 
                "✓ إدارة الطلبات والطاولات\n✓ تتبع المخزون الذكي\n✓ إدارة الفروع المتعددة\n✓ تقارير مفصلة\n✓ واجهة عربية حديثة\n✓ نظام صلاحيات متقدم", 360);

            contentPanel.Controls.AddRange(new Control[] { versionCard, developerCard, featuresCard });

            // Footer panel
            var footerPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = LightGray,
                Padding = new Padding(20)
            };

            // Close button
            var closeButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = PrimaryColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(100, 40),
                Location = new Point(250, 20),
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = SecondaryColor;
            closeButton.Click += (s, e) => this.Close();

            // Website link
            var websiteLabel = new LinkLabel
            {
                Text = "🌐 موقع الشركة",
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 30),
                Size = new Size(150, 20),
                LinkColor = PrimaryColor,
                ActiveLinkColor = SecondaryColor
            };
            websiteLabel.Click += (s, e) => System.Diagnostics.Process.Start("https://example.com");

            // Support email
            var supportLabel = new LinkLabel
            {
                Text = "📧 الدعم الفني",
                Font = new Font("Segoe UI", 10F),
                Location = new Point(400, 30),
                Size = new Size(150, 20),
                LinkColor = PrimaryColor,
                ActiveLinkColor = SecondaryColor
            };
            supportLabel.Click += (s, e) => System.Diagnostics.Process.Start("mailto:<EMAIL>");

            footerPanel.Controls.AddRange(new Control[] { closeButton, websiteLabel, supportLabel });

            // Add all panels to form
            this.Controls.AddRange(new Control[] { headerPanel, contentPanel, footerPanel });
            
            this.ResumeLayout(false);
        }

        private Panel CreateInfoCard(string title, string content, int yPosition)
        {
            var card = new Panel
            {
                Location = new Point(0, yPosition),
                Size = new Size(520, 160),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add shadow effect
            card.Paint += (s, e) => DrawCardShadow(e.Graphics, card.ClientRectangle);

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(20, 15),
                Size = new Size(480, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Content
            var contentLabel = new Label
            {
                Text = content,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(100, 100, 100),
                Location = new Point(20, 50),
                Size = new Size(480, 100),
                TextAlign = ContentAlignment.TopLeft
            };

            // Accent line
            var accentLine = new Panel
            {
                BackColor = AccentColor,
                Location = new Point(0, 0),
                Size = new Size(5, card.Height)
            };

            card.Controls.AddRange(new Control[] { accentLine, titleLabel, contentLabel });
            return card;
        }

        private void HeaderPanel_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background
            var panel = (Panel)sender;
            var rect = panel.ClientRectangle;
            using (var brush = new LinearGradientBrush(rect, PrimaryColor, SecondaryColor, 45f))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        }

        private void DrawCardShadow(Graphics g, Rectangle bounds)
        {
            // Create subtle shadow effect
            var shadowBounds = new Rectangle(bounds.X + 2, bounds.Y + 2, bounds.Width, bounds.Height);
            using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
            {
                g.FillRectangle(shadowBrush, shadowBounds);
            }
        }
    }
}
