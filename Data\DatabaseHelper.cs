using System;
using System.Configuration;
using Microsoft.Data.SqlClient;

namespace RestaurantManagement.Data
{
    public class DatabaseHelper
    {
        private static readonly string ConnectionString = 
            ConfigurationManager.ConnectionStrings["RestaurantDB"]?.ConnectionString 
            ?? "Server=localhost;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;";

        public static void InitializeDatabase()
        {
            try
            {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                CreateDatabaseIfNotExists();
                
                // إنشاء الجداول
                using var connection = new SqlConnection(ConnectionString);
                connection.Open();

                CreateTables(connection);
                InsertDefaultData(connection);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        private static void CreateDatabaseIfNotExists()
        {
            var builder = new SqlConnectionStringBuilder(ConnectionString);
            string databaseName = builder.InitialCatalog;
            builder.InitialCatalog = "master";
            
            using var connection = new SqlConnection(builder.ConnectionString);
            connection.Open();
            
            string checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
            using var checkCommand = new SqlCommand(checkDbQuery, connection);
            int dbExists = (int)checkCommand.ExecuteScalar();
            
            if (dbExists == 0)
            {
                string createDbQuery = $"CREATE DATABASE [{databaseName}]";
                using var createCommand = new SqlCommand(createDbQuery, connection);
                createCommand.ExecuteNonQuery();
            }
        }

        private static void CreateTables(SqlConnection connection)
        {
            string[] createTableQueries = {
                // جدول الفروع
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
                CREATE TABLE Branches (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Address NVARCHAR(500) NOT NULL,
                    Phone NVARCHAR(20),
                    Email NVARCHAR(100),
                    Manager NVARCHAR(100),
                    IsActive BIT DEFAULT 1,
                    OpeningTime TIME NOT NULL,
                    ClosingTime TIME NOT NULL,
                    DeliveryArea NVARCHAR(500),
                    DeliveryRadius DECIMAL(5,2) DEFAULT 5.0,
                    CreatedDate DATETIME2 DEFAULT GETDATE()
                )",

                // جدول إعدادات الفروع
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BranchSettings' AND xtype='U')
                CREATE TABLE BranchSettings (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    TaxRate DECIMAL(5,4) DEFAULT 0.14,
                    ServiceCharge DECIMAL(5,4) DEFAULT 0.10,
                    DeliveryFee DECIMAL(10,2) DEFAULT 15.00,
                    MinimumOrderForDelivery DECIMAL(10,2) DEFAULT 50.00,
                    AcceptOnlineOrders BIT DEFAULT 1,
                    AcceptReservations BIT DEFAULT 1,
                    MaxReservationDays INT DEFAULT 30,
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id)
                )",

                // جدول الموظفين
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
                CREATE TABLE Employees (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    Name NVARCHAR(100) NOT NULL,
                    Phone NVARCHAR(20),
                    Email NVARCHAR(100),
                    Role INT NOT NULL,
                    Salary DECIMAL(10,2),
                    HireDate DATETIME2 NOT NULL,
                    IsActive BIT DEFAULT 1,
                    Username NVARCHAR(50) UNIQUE,
                    Password NVARCHAR(255),
                    CanAccessAllBranches BIT DEFAULT 0,
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id)
                )",

                // جدول العملاء
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
                CREATE TABLE Customers (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Phone NVARCHAR(20) UNIQUE NOT NULL,
                    Email NVARCHAR(100),
                    Address NVARCHAR(500),
                    BirthDate DATE,
                    RegistrationDate DATETIME2 DEFAULT GETDATE(),
                    TotalOrders INT DEFAULT 0,
                    TotalSpent DECIMAL(10,2) DEFAULT 0,
                    PreferredBranch NVARCHAR(100),
                    Notes NVARCHAR(500),
                    IsVIP BIT DEFAULT 0,
                    LoyaltyPoints DECIMAL(10,2) DEFAULT 0,
                    ReceivePromotions BIT DEFAULT 1
                )",

                // جدول قائمة الطعام
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MenuItems' AND xtype='U')
                CREATE TABLE MenuItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(500),
                    Price DECIMAL(10,2) NOT NULL,
                    Category NVARCHAR(50) NOT NULL,
                    IsAvailable BIT DEFAULT 1,
                    CreatedDate DATETIME2 DEFAULT GETDATE(),
                    ImagePath NVARCHAR(500)
                )",

                // جدول الطاولات
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Tables' AND xtype='U')
                CREATE TABLE Tables (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    Number INT NOT NULL,
                    Capacity INT NOT NULL,
                    Status INT DEFAULT 0,
                    Location NVARCHAR(100),
                    ReservationTime DATETIME2,
                    ReservedBy NVARCHAR(100),
                    ReservationPhone NVARCHAR(20),
                    QRCode NVARCHAR(100),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    UNIQUE(BranchId, Number)
                )",

                // جدول الطلبات
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Orders' AND xtype='U')
                CREATE TABLE Orders (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    TableNumber INT,
                    CustomerId INT,
                    CustomerName NVARCHAR(100),
                    CustomerPhone NVARCHAR(20),
                    CustomerEmail NVARCHAR(100),
                    CustomerAddress NVARCHAR(500),
                    Type INT NOT NULL,
                    Status INT DEFAULT 0,
                    OrderDate DATETIME2 DEFAULT GETDATE(),
                    SubTotal DECIMAL(10,2) NOT NULL,
                    Tax DECIMAL(10,2) DEFAULT 0,
                    ServiceCharge DECIMAL(10,2) DEFAULT 0,
                    DeliveryFee DECIMAL(10,2) DEFAULT 0,
                    Discount DECIMAL(10,2) DEFAULT 0,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    Notes NVARCHAR(500),
                    EmployeeId INT NOT NULL,
                    PaymentMethod NVARCHAR(50) DEFAULT N'نقدي',
                    IsPaid BIT DEFAULT 0,
                    DeliveryTime DATETIME2,
                    DeliveryDriverName NVARCHAR(100),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                )",

                // جدول عناصر الطلبات
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U')
                CREATE TABLE OrderItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    OrderId INT NOT NULL,
                    MenuItemId INT NOT NULL,
                    MenuItemName NVARCHAR(100) NOT NULL,
                    Price DECIMAL(10,2) NOT NULL,
                    Quantity INT NOT NULL,
                    Notes NVARCHAR(500),
                    FOREIGN KEY (OrderId) REFERENCES Orders(Id),
                    FOREIGN KEY (MenuItemId) REFERENCES MenuItems(Id)
                )",

                // جدول الحجوزات
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Reservations' AND xtype='U')
                CREATE TABLE Reservations (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    TableId INT NOT NULL,
                    TableNumber INT NOT NULL,
                    CustomerName NVARCHAR(100) NOT NULL,
                    CustomerPhone NVARCHAR(20) NOT NULL,
                    CustomerEmail NVARCHAR(100),
                    ReservationDate DATE NOT NULL,
                    ReservationTime TIME NOT NULL,
                    PartySize INT NOT NULL,
                    Notes NVARCHAR(500),
                    IsConfirmed BIT DEFAULT 0,
                    SendReminder BIT DEFAULT 1,
                    CreatedDate DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    FOREIGN KEY (TableId) REFERENCES Tables(Id)
                )",

                // جدول المخزون
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='InventoryItems' AND xtype='U')
                CREATE TABLE InventoryItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    Name NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(500),
                    Category NVARCHAR(50) NOT NULL,
                    Unit NVARCHAR(20) NOT NULL,
                    CurrentStock DECIMAL(10,3) NOT NULL,
                    MinimumStock DECIMAL(10,3) NOT NULL,
                    MaximumStock DECIMAL(10,3) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    Supplier NVARCHAR(100),
                    SupplierPhone NVARCHAR(20),
                    ExpiryDate DATE,
                    Barcode NVARCHAR(50),
                    LastUpdated DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id)
                )",

                // جدول حركات المخزون
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockTransactions' AND xtype='U')
                CREATE TABLE StockTransactions (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    InventoryItemId INT NOT NULL,
                    ItemName NVARCHAR(100) NOT NULL,
                    Quantity DECIMAL(10,3) NOT NULL,
                    TransactionType NVARCHAR(20) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    Notes NVARCHAR(500),
                    InvoiceNumber NVARCHAR(50),
                    TransferToBranchId INT,
                    TransactionDate DATETIME2 DEFAULT GETDATE(),
                    EmployeeId INT NOT NULL,
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    FOREIGN KEY (InventoryItemId) REFERENCES InventoryItems(Id),
                    FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
                    FOREIGN KEY (TransferToBranchId) REFERENCES Branches(Id)
                )",

                // جدول المناوبات
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Shifts' AND xtype='U')
                CREATE TABLE Shifts (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    EmployeeId INT NOT NULL,
                    StartTime DATETIME2 NOT NULL,
                    EndTime DATETIME2,
                    Notes NVARCHAR(500),
                    FOREIGN KEY (BranchId) REFERENCES Branches(Id),
                    FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                )",

                // جدول نقاط الولاء
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LoyaltyTransactions' AND xtype='U')
                CREATE TABLE LoyaltyTransactions (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    CustomerId INT NOT NULL,
                    Points DECIMAL(10,2) NOT NULL,
                    TransactionType NVARCHAR(20) NOT NULL,
                    Description NVARCHAR(200),
                    OrderId INT,
                    TransactionDate DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (OrderId) REFERENCES Orders(Id)
                )",

                // جدول الطلبات المعلقة
                @"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='HeldOrders' AND xtype='U')
                CREATE TABLE HeldOrders (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    TableNumber INT NOT NULL,
                    ItemName NVARCHAR(100) NOT NULL,
                    Price DECIMAL(10,2) NOT NULL,
                    Quantity INT NOT NULL,
                    OriginalPrice DECIMAL(10,2) DEFAULT 0,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    HeldAt DATETIME2 DEFAULT GETDATE()
                )"
            };

            foreach (string query in createTableQueries)
            {
                using var command = new SqlCommand(query, connection);
                command.ExecuteNonQuery();
            }
        }

        private static void InsertDefaultData(SqlConnection connection)
        {
            // التحقق من وجود بيانات افتراضية
            using var checkCommand = new SqlCommand("SELECT COUNT(*) FROM Branches", connection);
            int branchCount = (int)checkCommand.ExecuteScalar();

            if (branchCount == 0)
            {
                // إدراج فرع افتراضي
                string insertBranch = @"INSERT INTO Branches 
                    (Name, Address, Phone, Email, Manager, OpeningTime, ClosingTime, DeliveryArea) 
                    VALUES (@Name, @Address, @Phone, @Email, @Manager, @OpeningTime, @ClosingTime, @DeliveryArea)";
                
                using var branchCommand = new SqlCommand(insertBranch, connection);
                branchCommand.Parameters.AddWithValue("@Name", "الفرع الرئيسي");
                branchCommand.Parameters.AddWithValue("@Address", "شارع الجمهورية، القاهرة");
                branchCommand.Parameters.AddWithValue("@Phone", "01000000000");
                branchCommand.Parameters.AddWithValue("@Email", "<EMAIL>");
                branchCommand.Parameters.AddWithValue("@Manager", "أحمد محمد");
                branchCommand.Parameters.AddWithValue("@OpeningTime", TimeSpan.Parse("08:00:00"));
                branchCommand.Parameters.AddWithValue("@ClosingTime", TimeSpan.Parse("23:00:00"));
                branchCommand.Parameters.AddWithValue("@DeliveryArea", "القاهرة الجديدة، مدينة نصر، الزمالك");
                branchCommand.ExecuteNonQuery();

                // الحصول على معرف الفرع المُدرج
                using var getBranchIdCommand = new SqlCommand("SELECT SCOPE_IDENTITY()", connection);
                int branchId = Convert.ToInt32(getBranchIdCommand.ExecuteScalar());

                // إدراج إعدادات الفرع
                string insertBranchSettings = @"INSERT INTO BranchSettings (BranchId) VALUES (@BranchId)";
                using var settingsCommand = new SqlCommand(insertBranchSettings, connection);
                settingsCommand.Parameters.AddWithValue("@BranchId", branchId);
                settingsCommand.ExecuteNonQuery();

                // إدراج موظف افتراضي (مدير)
                string insertEmployee = @"INSERT INTO Employees 
                    (BranchId, Name, Phone, Email, Role, Salary, HireDate, Username, Password, CanAccessAllBranches) 
                    VALUES (@BranchId, @Name, @Phone, @Email, @Role, @Salary, GETDATE(), @Username, @Password, @CanAccessAllBranches)";
                
                using var empCommand = new SqlCommand(insertEmployee, connection);
                empCommand.Parameters.AddWithValue("@BranchId", branchId);
                empCommand.Parameters.AddWithValue("@Name", "مدير النظام");
                empCommand.Parameters.AddWithValue("@Phone", "01000000000");
                empCommand.Parameters.AddWithValue("@Email", "<EMAIL>");
                empCommand.Parameters.AddWithValue("@Role", 0);
                empCommand.Parameters.AddWithValue("@Salary", 8000);
                empCommand.Parameters.AddWithValue("@Username", "admin");
                empCommand.Parameters.AddWithValue("@Password", "admin123");
                empCommand.Parameters.AddWithValue("@CanAccessAllBranches", true);
                empCommand.ExecuteNonQuery();

                // إدراج طاولات افتراضية
                for (int i = 1; i <= 25; i++)
                {
                    string insertTable = @"INSERT INTO Tables (BranchId, Number, Capacity, Location, QRCode) 
                        VALUES (@BranchId, @Number, @Capacity, @Location, @QRCode)";
                    
                    using var tableCommand = new SqlCommand(insertTable, connection);
                    tableCommand.Parameters.Clear();
                    tableCommand.Parameters.AddWithValue("@BranchId", branchId);
                    tableCommand.Parameters.AddWithValue("@Number", i);
                    tableCommand.Parameters.AddWithValue("@Capacity", i <= 10 ? 2 : i <= 20 ? 4 : 6);
                    tableCommand.Parameters.AddWithValue("@Location", i <= 12 ? "الطابق الأول" : "الطابق الثاني");
                    tableCommand.Parameters.AddWithValue("@QRCode", $"TABLE_{branchId}_{i}");
                    tableCommand.ExecuteNonQuery();
                }

                // إدراج عناصر قائمة طعام افتراضية
                var menuItems = new[]
                {
                    new { Name = "برجر لحم كلاسيك", Description = "برجر لحم بقري مشوي مع الخضار والصوص الخاص", Price = 55.00m, Category = "وجبات رئيسية" },
                    new { Name = "برجر دجاج مشوي", Description = "برجر دجاج مشوي مع الخس والطماطم", Price = 45.00m, Category = "وجبات رئيسية" },
                    new { Name = "بيتزا مارجريتا", Description = "بيتزا بالجبن الموتزاريلا والطماطم والريحان", Price = 65.00m, Category = "بيتزا" },
                    new { Name = "بيتزا بيبروني", Description = "بيتزا بالبيبروني والجبن والفلفل الحار", Price = 75.00m, Category = "بيتزا" },
                    new { Name = "بيتزا الخضار", Description = "بيتزا بالخضار المشكلة والجبن", Price = 60.00m, Category = "بيتزا" },
                    new { Name = "سلطة يونانية", Description = "سلطة خضار طازجة مع الجبن الأبيض والزيتون", Price = 35.00m, Category = "سلطات" },
                    new { Name = "سلطة سيزر", Description = "سلطة الخس مع صوص السيزر والدجاج المشوي", Price = 40.00m, Category = "سلطات" },
                    new { Name = "شوربة الدجاج", Description = "شوربة دجاج ساخنة بالخضار", Price = 25.00m, Category = "شوربات" },
                    new { Name = "شوربة العدس", Description = "شوربة عدس أحمر بالخضار والتوابل", Price = 20.00m, Category = "شوربات" },
                    new { Name = "عصير برتقال طازج", Description = "عصير برتقال طبيعي 100%", Price = 18.00m, Category = "مشروبات طبيعية" },
                    new { Name = "عصير مانجو", Description = "عصير مانجو طازج", Price = 20.00m, Category = "مشروبات طبيعية" },
                    new { Name = "كوكا كولا", Description = "مشروب غازي - علبة 330 مل", Price = 12.00m, Category = "مشروبات غازية" },
                    new { Name = "بيبسي", Description = "مشروب غازي - علبة 330 مل", Price = 12.00m, Category = "مشروبات غازية" },
                    new { Name = "شاي أحمر", Description = "شاي أحمر مع السكر", Price = 10.00m, Category = "مشروبات ساخنة" },
                    new { Name = "قهوة تركية", Description = "قهوة تركية أصيلة", Price = 15.00m, Category = "مشروبات ساخنة" },
                    new { Name = "كابتشينو", Description = "قهوة كابتشينو بالحليب", Price = 25.00m, Category = "مشروبات ساخنة" },
                    new { Name = "تشيز كيك", Description = "قطعة تشيز كيك بالفراولة", Price = 30.00m, Category = "حلويات" },
                    new { Name = "تيراميسو", Description = "حلوى تيراميسو إيطالية", Price = 35.00m, Category = "حلويات" }
                };

                string insertMenuItemQuery = @"INSERT INTO MenuItems 
                    (Name, Description, Price, Category, IsAvailable, CreatedDate, ImagePath) 
                    VALUES (@Name, @Description, @Price, @Category, 1, GETDATE(), '')";

                foreach (var item in menuItems)
                {
                    using var menuCommand = new SqlCommand(insertMenuItemQuery, connection);
                    menuCommand.Parameters.Clear();
                    menuCommand.Parameters.AddWithValue("@Name", item.Name);
                    menuCommand.Parameters.AddWithValue("@Description", item.Description);
                    menuCommand.Parameters.AddWithValue("@Price", item.Price);
                    menuCommand.Parameters.AddWithValue("@Category", item.Category);
                    menuCommand.ExecuteNonQuery();
                }

                // إدراج عناصر مخزون افتراضية
                var inventoryItems = new[]
                {
                    new { Name = "لحم بقري", Description = "لحم بقري طازج للبرجر", Category = "لحوم", Unit = "كيلو", CurrentStock = 50.0m, MinimumStock = 10.0m, MaximumStock = 100.0m, UnitCost = 120.00m, Supplier = "مزرعة الأهرام", SupplierPhone = "01111111111", ExpiryDate = "2024-12-31" },
                    new { Name = "دجاج مجمد", Description = "قطع دجاج مجمدة", Category = "لحوم", Unit = "كيلو", CurrentStock = 30.0m, MinimumStock = 5.0m, MaximumStock = 80.0m, UnitCost = 45.00m, Supplier = "شركة الدواجن المصرية", SupplierPhone = "01222222222", ExpiryDate = "2024-06-30" },
                    new { Name = "جبن موتزاريلا", Description = "جبن موتزاريلا للبيتزا", Category = "ألبان", Unit = "كيلو", CurrentStock = 20.0m, MinimumStock = 3.0m, MaximumStock = 50.0m, UnitCost = 85.00m, Supplier = "مصنع الألبان", SupplierPhone = "01333333333", ExpiryDate = "2024-03-15" },
                    new { Name = "طماطم", Description = "طماطم طازجة", Category = "خضروات", Unit = "كيلو", CurrentStock = 25.0m, MinimumStock = 5.0m, MaximumStock = 60.0m, UnitCost = 8.00m, Supplier = "سوق الخضار", SupplierPhone = "01444444444", ExpiryDate = "2024-02-10" },
                    new { Name = "خس", Description = "خس طازج للسلطات", Category = "خضروات", Unit = "كيلو", CurrentStock = 15.0m, MinimumStock = 3.0m, MaximumStock = 40.0m, UnitCost = 12.00m, Supplier = "سوق الخضار", SupplierPhone = "01444444444", ExpiryDate = "2024-02-05" },
                    new { Name = "كوكا كولا", Description = "علب كوكا كولا 330 مل", Category = "مشروبات", Unit = "علبة", CurrentStock = 100.0m, MinimumStock = 20.0m, MaximumStock = 200.0m, UnitCost = 8.00m, Supplier = "شركة المشروبات", SupplierPhone = "01555555555", ExpiryDate = "2025-12-31" }
                };

                string insertInventoryItemQuery = @"INSERT INTO InventoryItems 
                    (BranchId, Name, Description, Category, Unit, CurrentStock, MinimumStock, MaximumStock, UnitCost, Supplier, SupplierPhone, ExpiryDate) 
                    VALUES (@BranchId, @Name, @Description, @Category, @Unit, @CurrentStock, @MinimumStock, @MaximumStock, @UnitCost, @Supplier, @SupplierPhone, @ExpiryDate)";

                foreach (var item in inventoryItems)
                {
                    using var inventoryCommand = new SqlCommand(insertInventoryItemQuery, connection);
                    inventoryCommand.Parameters.Clear();
                    inventoryCommand.Parameters.AddWithValue("@BranchId", branchId);
                    inventoryCommand.Parameters.AddWithValue("@Name", item.Name);
                    inventoryCommand.Parameters.AddWithValue("@Description", item.Description);
                    inventoryCommand.Parameters.AddWithValue("@Category", item.Category);
                    inventoryCommand.Parameters.AddWithValue("@Unit", item.Unit);
                    inventoryCommand.Parameters.AddWithValue("@CurrentStock", item.CurrentStock);
                    inventoryCommand.Parameters.AddWithValue("@MinimumStock", item.MinimumStock);
                    inventoryCommand.Parameters.AddWithValue("@MaximumStock", item.MaximumStock);
                    inventoryCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                    inventoryCommand.Parameters.AddWithValue("@Supplier", item.Supplier);
                    inventoryCommand.Parameters.AddWithValue("@SupplierPhone", item.SupplierPhone);
                    inventoryCommand.Parameters.AddWithValue("@ExpiryDate", DateTime.Parse(item.ExpiryDate));
                    inventoryCommand.ExecuteNonQuery();
                }
            }
        }

        public static SqlConnection GetConnection()
        {
            return new SqlConnection(ConnectionString);
        }
    }
}