using System;

namespace RestaurantManagement.Models
{
    public class InventoryItem
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // خضار، لحوم، مشروبات، إلخ
        public string Unit { get; set; } = string.Empty; // كيلو، لتر، قطعة، إلخ
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal MaximumStock { get; set; }
        public decimal UnitCost { get; set; }
        public string Supplier { get; set; } = string.Empty;
        public string SupplierPhone { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public bool IsLowStock => CurrentStock <= MinimumStock;
        public bool IsExpiringSoon => ExpiryDate <= DateTime.Now.AddDays(7);
        public string Barcode { get; set; } = string.Empty;
    }

    public class StockTransaction
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public int InventoryItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public string TransactionType { get; set; } = string.Empty; // Purchase, Usage, Adjustment, Transfer
        public decimal UnitCost { get; set; }
        public decimal TotalCost => Quantity * UnitCost;
        public string Notes { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string InvoiceNumber { get; set; } = string.Empty;
        public int? TransferToBranchId { get; set; } // للنقل بين الفروع
        public string TransferToBranchName { get; set; } = string.Empty;
    }
}