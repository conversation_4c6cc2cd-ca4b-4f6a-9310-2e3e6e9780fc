@echo off
chcp 65001 >nul
echo ========================================
echo    فحص بيئة التطوير - Environment Check
echo ========================================
echo.

echo 1. فحص إصدار .NET:
dotnet --version
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت أو غير متاح
    echo يرجى تحميل وتثبيت .NET 6.0 من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
) else (
    echo ✅ .NET متاح
)
echo.

echo 2. فحص معلومات .NET التفصيلية:
dotnet --info
echo.

echo 3. فحص ملفات المشروع:
if exist "RestaurantManagement.csproj" (
    echo ✅ ملف المشروع موجود: RestaurantManagement.csproj
) else (
    echo ❌ ملف المشروع غير موجود: RestaurantManagement.csproj
)

if exist "RestaurantManagement.sln" (
    echo ✅ ملف الحل موجود: RestaurantManagement.sln
) else (
    echo ❌ ملف الحل غير موجود: RestaurantManagement.sln
)

if exist "App.config" (
    echo ✅ ملف الإعدادات موجود: App.config
) else (
    echo ❌ ملف الإعدادات غير موجود: App.config
)

if exist "Program.cs" (
    echo ✅ ملف البرنامج الرئيسي موجود: Program.cs
) else (
    echo ❌ ملف البرنامج الرئيسي غير موجود: Program.cs
)
echo.

echo 4. فحص المجلدات:
if exist "Models" (
    echo ✅ مجلد Models موجود
) else (
    echo ❌ مجلد Models غير موجود
)

if exist "Data" (
    echo ✅ مجلد Data موجود
) else (
    echo ❌ مجلد Data غير موجود
)

if exist "Services" (
    echo ✅ مجلد Services موجود
) else (
    echo ❌ مجلد Services غير موجود
)

if exist "Forms" (
    echo ✅ مجلد Forms موجود
) else (
    echo ❌ مجلد Forms غير موجود
)
echo.

echo 5. فحص SQL Server:
sqlcmd -S localhost -E -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SQL Server متاح ويعمل
    sqlcmd -S localhost -E -Q "SELECT name FROM sys.databases WHERE name = 'RestaurantManagement'" -h -1 | findstr "RestaurantManagement" >nul
    if %errorlevel% equ 0 (
        echo ✅ قاعدة البيانات RestaurantManagement موجودة
    ) else (
        echo ⚠️  قاعدة البيانات RestaurantManagement غير موجودة
        echo يرجى تشغيل setup_database.sql
    )
) else (
    echo ❌ SQL Server غير متاح أو لا يعمل
    echo تأكد من تشغيل SQL Server Service
)
echo.

echo 6. اختبار بناء المشروع:
if exist "RestaurantManagement.csproj" (
    echo جاري اختبار بناء المشروع...
    dotnet build RestaurantManagement.csproj --verbosity quiet --nologo
    if %errorlevel% equ 0 (
        echo ✅ المشروع يبنى بنجاح
    ) else (
        echo ❌ خطأ في بناء المشروع
        echo شغّل: dotnet build RestaurantManagement.csproj لمعرفة التفاصيل
    )
) else (
    echo ❌ لا يمكن اختبار البناء - ملف المشروع غير موجود
)
echo.

echo ========================================
echo انتهى فحص البيئة
echo ========================================

pause