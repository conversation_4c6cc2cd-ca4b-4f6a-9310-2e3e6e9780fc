using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using RestaurantManagement.Data;

namespace RestaurantManagement.Forms
{
    public partial class ModernSettingsForm : Form
    {
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color SuccessColor = Color.FromArgb(39, 174, 96);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        public ModernSettingsForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "الإعدادات - نظام إدارة المطعم";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = LightGray;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Header panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = PrimaryColor
            };
            headerPanel.Paint += HeaderPanel_Paint;

            var titleLabel = new Label
            {
                Text = "⚙️ إعدادات النظام",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                AutoSize = true
            };

            headerPanel.Controls.Add(titleLabel);

            // Main content panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30),
                BackColor = LightGray
            };

            // Database settings card
            var dbCard = CreateSettingsCard("🗄️ إعدادات قاعدة البيانات", 0);
            var resetMenuBtn = CreateActionButton("إعادة تعيين قائمة الطعام", WarningColor, 20, 50);
            var resetAllBtn = CreateActionButton("إعادة تعيين جميع البيانات", DangerColor, 20, 100);
            var backupBtn = CreateActionButton("نسخ احتياطي للبيانات", SuccessColor, 20, 150);

            resetMenuBtn.Click += ResetMenuBtn_Click;
            resetAllBtn.Click += ResetAllBtn_Click;
            backupBtn.Click += BackupBtn_Click;

            dbCard.Controls.AddRange(new Control[] { resetMenuBtn, resetAllBtn, backupBtn });

            // System settings card
            var systemCard = CreateSettingsCard("🖥️ إعدادات النظام", 220);
            var themeLabel = new Label
            {
                Text = "المظهر:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 50),
                Size = new Size(80, 25)
            };

            var themeCombo = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(110, 50),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            themeCombo.Items.AddRange(new[] { "فاتح", "داكن", "تلقائي" });
            themeCombo.SelectedIndex = 0;

            var languageLabel = new Label
            {
                Text = "اللغة:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 90),
                Size = new Size(80, 25)
            };

            var languageCombo = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(110, 90),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            languageCombo.Items.AddRange(new[] { "العربية", "English" });
            languageCombo.SelectedIndex = 0;

            systemCard.Controls.AddRange(new Control[] { themeLabel, themeCombo, languageLabel, languageCombo });

            mainPanel.Controls.AddRange(new Control[] { dbCard, systemCard });

            // Footer panel
            var footerPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = LightGray,
                Padding = new Padding(30, 20, 30, 20)
            };

            var saveButton = new Button
            {
                Text = "💾 حفظ الإعدادات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = SuccessColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(150, 40),
                Location = new Point(30, 20),
                Cursor = Cursors.Hand
            };
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;

            var cancelButton = new Button
            {
                Text = "❌ إلغاء",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Gray,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(100, 40),
                Location = new Point(190, 20),
                Cursor = Cursors.Hand
            };
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Click += (s, e) => this.Close();

            var aboutButton = new Button
            {
                Text = "ℹ️ حول البرنامج",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = PrimaryColor,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(130, 40),
                Location = new Point(500, 20),
                Cursor = Cursors.Hand
            };
            aboutButton.FlatAppearance.BorderSize = 0;
            aboutButton.Click += AboutButton_Click;

            footerPanel.Controls.AddRange(new Control[] { saveButton, cancelButton, aboutButton });

            // Add all panels to form
            this.Controls.AddRange(new Control[] { headerPanel, mainPanel, footerPanel });
            
            this.ResumeLayout(false);
        }

        private Panel CreateSettingsCard(string title, int yPosition)
        {
            var card = new Panel
            {
                Location = new Point(0, yPosition),
                Size = new Size(740, 200),
                BackColor = Color.White,
                Margin = new Padding(0, 0, 0, 20)
            };

            // Add shadow effect
            card.Paint += (s, e) => DrawCardShadow(e.Graphics, card.ClientRectangle);

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(20, 15),
                Size = new Size(700, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Accent line
            var accentLine = new Panel
            {
                BackColor = AccentColor,
                Location = new Point(0, 0),
                Size = new Size(5, card.Height)
            };

            card.Controls.AddRange(new Control[] { accentLine, titleLabel });
            return card;
        }

        private Button CreateActionButton(string text, Color color, int x, int y)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = color,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(x, y),
                Size = new Size(200, 35),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(color, 0.1f);

            // Add hover effect
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Light(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }

        private void HeaderPanel_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background
            var panel = (Panel)sender;
            var rect = panel.ClientRectangle;
            using (var brush = new LinearGradientBrush(rect, PrimaryColor, SecondaryColor, 45f))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        }

        private void DrawCardShadow(Graphics g, Rectangle bounds)
        {
            // Create subtle shadow effect
            var shadowBounds = new Rectangle(bounds.X + 3, bounds.Y + 3, bounds.Width, bounds.Height);
            using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
            {
                g.FillRectangle(shadowBrush, shadowBounds);
            }
        }

        private void ResetMenuBtn_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إعادة تعيين قائمة الطعام لإصلاح مشاكل الترميز العربي؟\n\nسيتم حذف جميع الأصناف الحالية وإعادة إدراج أصناف جديدة بالترميز الصحيح.",
                "إعادة تعيين قائمة الطعام",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseInitializer.ResetMenuItems();
                    MessageBox.Show("تم إعادة تعيين قائمة الطعام بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين قائمة الطعام: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ResetAllBtn_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "تحذير: هذا سيحذف جميع البيانات (الفروع، الموظفين، قائمة الطعام، الطلبات) وإعادة إدراج البيانات الأولية بالترميز العربي الصحيح.\n\nهل أنت متأكد؟",
                "إعادة تعيين جميع البيانات",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseInitializer.ResetAllData();
                    MessageBox.Show("تم إعادة تعيين جميع البيانات بنجاح!\n\nيرجى إعادة تشغيل البرنامج.", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    Application.Exit();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين البيانات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BackupBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "SQL Backup Files (*.bak)|*.bak|All Files (*.*)|*.*",
                    Title = "حفظ نسخة احتياطية",
                    FileName = $"RestaurantDB_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // Placeholder for backup functionality
                    MessageBox.Show("سيتم إضافة وظيفة النسخ الاحتياطي قريباً", "قيد التطوير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في النسخ الاحتياطي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AboutButton_Click(object sender, EventArgs e)
        {
            var aboutForm = new AboutForm();
            aboutForm.ShowDialog();
        }
    }
}
