# إعداد المشروع - Project Setup Guide

## 🎯 **تم إصلاح مشكلة ملفات المشروع!**

### ✅ **الملفات الجديدة المضافة:**
- `RestaurantManagement.csproj` - ملف المشروع الرئيسي
- `RestaurantManagement.sln` - ملف الحل (Solution)
- `global.json` - إعدادات .NET SDK
- `Directory.Build.props` - إعدادات البناء
- `build.bat` - ملف بناء المشروع
- `check_environment.bat` - فحص البيئة

---

## 🚀 **طرق التشغيل الجديدة:**

### **1. الطريقة المُوصى بها (خطوة بخطوة):**
```cmd
# 1. فحص البيئة أولاً
check_environment.bat

# 2. بناء المشروع
build.bat

# 3. تشغيل التطبيق
run.bat
```

### **2. الطريقة السريعة (PowerShell):**
```powershell
# تشغيل مباشر مع فحص تلقائي
.\run.ps1
```

### **3. الطريقة اليدوية:**
```cmd
# بناء وتشغيل يدوي
dotnet restore RestaurantManagement.csproj
dotnet build RestaurantManagement.csproj --configuration Release
dotnet run --project RestaurantManagement.csproj
```

---

## 🔧 **حل المشاكل الشائعة:**

### **مشكلة: "Specify a project or solution file"**
```
السبب: عدم وجود ملف .csproj في المجلد
الحل: تم إنشاء ملف RestaurantManagement.csproj
```

### **مشكلة: "Package restore failed"**
```cmd
# تأكد من اتصال الإنترنت وشغّل:
dotnet restore RestaurantManagement.csproj --force
```

### **مشكلة: "Build failed"**
```cmd
# نظّف وأعد البناء:
dotnet clean RestaurantManagement.csproj
dotnet build RestaurantManagement.csproj --configuration Release
```

---

## 📁 **هيكل المشروع المحدث:**

```
RestaurantManagement/
├── RestaurantManagement.csproj    # ملف المشروع الرئيسي ✨ جديد
├── RestaurantManagement.sln       # ملف الحل ✨ جديد
├── global.json                    # إعدادات .NET ✨ جديد
├── Directory.Build.props          # إعدادات البناء ✨ جديد
├── Program.cs                     # نقطة البداية
├── App.config                     # إعدادات التطبيق
├── Models/                        # نماذج البيانات
├── Data/                          # طبقة البيانات
├── Services/                      # طبقة الخدمات
├── Forms/                         # واجهات المستخدم
├── build.bat                      # بناء المشروع ✨ جديد
├── run.bat                        # تشغيل محدث ✨
├── run.ps1                        # تشغيل PowerShell محدث ✨
├── check_environment.bat          # فحص البيئة ✨ جديد
├── setup_database.sql             # إعداد قاعدة البيانات
├── test_encoding.sql              # اختبار الترميز
└── README.md                      # الدليل الشامل
```

---

## 🎯 **اختبار سريع:**

### **1. فحص البيئة:**
```cmd
check_environment.bat
```
يجب أن ترى:
- ✅ .NET متاح
- ✅ ملف المشروع موجود
- ✅ SQL Server متاح
- ✅ المشروع يبنى بنجاح

### **2. بناء المشروع:**
```cmd
build.bat
```
يجب أن ترى:
- تم استعادة الحزم بنجاح
- تم بناء المشروع بنجاح

### **3. تشغيل التطبيق:**
```cmd
run.bat
```
يجب أن يفتح التطبيق مع واجهة تسجيل الدخول

---

## 📋 **قائمة التحقق:**

- [ ] تم تشغيل `check_environment.bat` بنجاح
- [ ] جميع الملفات المطلوبة موجودة
- [ ] .NET 6.0 مثبت ويعمل
- [ ] SQL Server يعمل
- [ ] قاعدة البيانات مُعدة (شغّل `setup_database.sql`)
- [ ] المشروع يبنى بدون أخطاء
- [ ] التطبيق يفتح ويعرض واجهة تسجيل الدخول

---

## 🎉 **بيانات تسجيل الدخول:**

بعد التشغيل الناجح:
- **الفرع**: الفرع الرئيسي
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 💡 **نصائح إضافية:**

### **للمطورين:**
```cmd
# فتح المشروع في Visual Studio
start RestaurantManagement.sln

# أو في VS Code
code .
```

### **للنشر:**
```cmd
# إنشاء نسخة للنشر
dotnet publish RestaurantManagement.csproj -c Release -o publish
```

### **للتشخيص:**
```cmd
# تشغيل مع تفاصيل كاملة
dotnet run --project RestaurantManagement.csproj --verbosity diagnostic
```

---

**المشروع الآن جاهز للتشغيل بدون مشاكل! 🎊**

إذا واجهت أي مشكلة، راجع:
- `TROUBLESHOOTING.md` - دليل حل المشاكل
- `ENCODING_FIX.md` - حلول مشاكل الترميز
- `QUICK_START.md` - دليل التشغيل السريع