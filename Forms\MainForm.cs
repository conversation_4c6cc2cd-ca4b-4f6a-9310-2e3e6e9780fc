using System;
using System.Drawing;
using System.Windows.Forms;
using RestaurantManagement.Data;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class MainForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private BranchService branchService;
        
        public MainForm()
        {
            InitializeComponent();
            branchService = new BranchService();
            InitializeDatabase();
        }

        public void SetCurrentUser(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            
            if (employee != null && branch != null)
            {
                this.Text = $"نظام إدارة المطعم - {employee.Name} - {branch.Name}";
            }
        }

        private void InitializeDatabase()
        {
            try
            {
                DatabaseHelper.InitializeDatabase();
                MessageBox.Show("تم تهيئة قاعدة البيانات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // MainForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.Name = "MainForm";
            this.Text = "نظام إدارة المطعم - الشاشة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // إنشاء شريط القوائم
            CreateMenuBar();
            
            // إنشاء شريط الأدوات
            CreateToolBar();
            
            // إنشاء شريط الحالة
            CreateStatusBar();
            
            // إنشاء اللوحة الرئيسية
            CreateMainPanel();
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private MenuStrip menuStrip;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private Panel mainPanel;
        private Label welcomeLabel;

        private void CreateMenuBar()
        {
            menuStrip = new MenuStrip();
            
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("تسجيل الدخول", null, LoginMenuItem_Click);
            fileMenu.DropDownItems.Add("تسجيل الخروج", null, LogoutMenuItem_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("إعدادات", null, SettingsMenuItem_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("خروج", null, ExitMenuItem_Click);
            
            // قائمة الطلبات
            var ordersMenu = new ToolStripMenuItem("الطلبات");
            ordersMenu.DropDownItems.Add("طلب جديد", null, NewOrderMenuItem_Click);
            ordersMenu.DropDownItems.Add("عرض الطلبات", null, ViewOrdersMenuItem_Click);
            ordersMenu.DropDownItems.Add("حالة الطلبات", null, OrderStatusMenuItem_Click);
            
            // قائمة القوائم
            var menuMenu = new ToolStripMenuItem("قائمة الطعام");
            menuMenu.DropDownItems.Add("إدارة القائمة", null, ManageMenuMenuItem_Click);
            menuMenu.DropDownItems.Add("الفئات", null, CategoriesMenuItem_Click);
            
            // قائمة الطاولات
            var tablesMenu = new ToolStripMenuItem("الطاولات");
            tablesMenu.DropDownItems.Add("عرض الطاولات", null, ViewTablesMenuItem_Click);
            tablesMenu.DropDownItems.Add("الحجوزات", null, ReservationsMenuItem_Click);
            
            // قائمة المخزون
            var inventoryMenu = new ToolStripMenuItem("المخزون");
            inventoryMenu.DropDownItems.Add("إدارة المخزون", null, ManageInventoryMenuItem_Click);
            inventoryMenu.DropDownItems.Add("حركات المخزون", null, StockTransactionsMenuItem_Click);
            inventoryMenu.DropDownItems.Add("تقرير المخزون", null, InventoryReportMenuItem_Click);
            
            // قائمة الموظفين
            var employeesMenu = new ToolStripMenuItem("الموظفين");
            employeesMenu.DropDownItems.Add("إدارة الموظفين", null, ManageEmployeesMenuItem_Click);
            employeesMenu.DropDownItems.Add("المناوبات", null, ShiftsMenuItem_Click);
            
            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("تقرير المبيعات", null, SalesReportMenuItem_Click);
            reportsMenu.DropDownItems.Add("تقرير الأرباح", null, ProfitReportMenuItem_Click);
            reportsMenu.DropDownItems.Add("أداء الموظفين", null, EmployeePerformanceMenuItem_Click);
            
            // قائمة الفروع (للمديرين فقط)
            var branchesMenu = new ToolStripMenuItem("الفروع");
            branchesMenu.DropDownItems.Add("إدارة الفروع", null, ManageBranchesMenuItem_Click);
            branchesMenu.DropDownItems.Add("تبديل الفرع", null, SwitchBranchMenuItem_Click);
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, AboutMenuItem_Click);
            helpMenu.DropDownItems.Add("دليل المستخدم", null, UserGuideMenuItem_Click);
            
            menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu, ordersMenu, menuMenu, tablesMenu, 
                inventoryMenu, employeesMenu, reportsMenu, branchesMenu, helpMenu
            });
            
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateToolBar()
        {
            toolStrip = new ToolStrip();
            toolStrip.ImageScalingSize = new Size(32, 32);
            
            var newOrderButton = new ToolStripButton("طلب جديد");
            newOrderButton.Click += NewOrderMenuItem_Click;
            
            var viewOrdersButton = new ToolStripButton("عرض الطلبات");
            viewOrdersButton.Click += ViewOrdersMenuItem_Click;
            
            var tablesButton = new ToolStripButton("الطاولات");
            tablesButton.Click += ViewTablesMenuItem_Click;
            
            var inventoryButton = new ToolStripButton("المخزون");
            inventoryButton.Click += ManageInventoryMenuItem_Click;
            
            var reportsButton = new ToolStripButton("التقارير");
            reportsButton.Click += SalesReportMenuItem_Click;
            
            toolStrip.Items.AddRange(new ToolStripItem[] {
                newOrderButton, new ToolStripSeparator(),
                viewOrdersButton, new ToolStripSeparator(),
                tablesButton, new ToolStripSeparator(),
                inventoryButton, new ToolStripSeparator(),
                reportsButton
            });
            
            this.Controls.Add(toolStrip);
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip();
            
            var userLabel = new ToolStripStatusLabel("المستخدم: غير مسجل");
            var branchLabel = new ToolStripStatusLabel("الفرع: غير محدد");
            var timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd HH:mm"));
            
            statusStrip.Items.AddRange(new ToolStripItem[] {
                userLabel, new ToolStripStatusLabel(" | "),
                branchLabel, new ToolStripStatusLabel(" | "),
                timeLabel
            });
            
            // تحديث الوقت كل ثانية
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000;
            timer.Tick += (s, e) => timeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            timer.Start();
            
            this.Controls.Add(statusStrip);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.BackColor = Color.WhiteSmoke;
            
            welcomeLabel = new Label();
            welcomeLabel.Text = "مرحباً بك في نظام إدارة المطعم\n\nيرجى تسجيل الدخول للبدء";
            welcomeLabel.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            welcomeLabel.ForeColor = Color.DarkBlue;
            welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;
            welcomeLabel.Dock = DockStyle.Fill;
            
            mainPanel.Controls.Add(welcomeLabel);
            this.Controls.Add(mainPanel);
        }

        // Event Handlers
        private void LoginMenuItem_Click(object? sender, EventArgs e)
        {
            var loginForm = new LoginForm();
            if (loginForm.ShowDialog() == DialogResult.OK)
            {
                currentEmployee = loginForm.LoggedInEmployee;
                currentBranch = loginForm.SelectedBranch;
                UpdateUI();
            }
        }

        private void LogoutMenuItem_Click(object? sender, EventArgs e)
        {
            currentEmployee = null;
            currentBranch = null;
            UpdateUI();
        }

        private void UpdateUI()
        {
            if (currentEmployee != null && currentBranch != null)
            {
                welcomeLabel.Text = $"مرحباً {currentEmployee.Name}\nالفرع: {currentBranch.Name}";
                statusStrip.Items[0].Text = $"المستخدم: {currentEmployee.Name}";
                statusStrip.Items[2].Text = $"الفرع: {currentBranch.Name}";
                
                // تفعيل القوائم حسب صلاحيات المستخدم
                EnableMenusBasedOnRole();
            }
            else
            {
                welcomeLabel.Text = "مرحباً بك في نظام إدارة المطعم\n\nيرجى تسجيل الدخول للبدء";
                statusStrip.Items[0].Text = "المستخدم: غير مسجل";
                statusStrip.Items[2].Text = "الفرع: غير محدد";
                
                // تعطيل جميع القوائم
                DisableAllMenus();
            }
        }

        private void EnableMenusBasedOnRole()
        {
            // تفعيل القوائم الأساسية
            foreach (ToolStripMenuItem menu in menuStrip.Items)
            {
                menu.Enabled = true;
            }
            
            // تقييد الوصول حسب الدور
            if (currentEmployee?.Role != EmployeeRole.Manager)
            {
                // إخفاء قائمة الفروع للموظفين العاديين
                menuStrip.Items[7].Visible = false; // قائمة الفروع
            }
        }

        private void DisableAllMenus()
        {
            foreach (ToolStripMenuItem menu in menuStrip.Items)
            {
                if (menu.Text != "ملف" && menu.Text != "مساعدة")
                {
                    menu.Enabled = false;
                }
            }
        }

        // Event handlers
        private void SettingsMenuItem_Click(object? sender, EventArgs e)
        {
            var settingsForm = new Form
            {
                Text = "إعدادات النظام",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var resetMenuButton = new Button
            {
                Text = "إعادة تعيين قائمة الطعام",
                Location = new Point(50, 50),
                Size = new Size(300, 40),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };

            var resetAllButton = new Button
            {
                Text = "إعادة تعيين جميع البيانات",
                Location = new Point(50, 100),
                Size = new Size(300, 40),
                BackColor = Color.Red,
                ForeColor = Color.White
            };

            var closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(50, 200),
                Size = new Size(300, 40),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };

            resetMenuButton.Click += (s, args) =>
            {
                var result = MessageBox.Show(
                    "هل تريد إعادة تعيين قائمة الطعام لإصلاح مشاكل الترميز العربي؟\n\nسيتم حذف جميع الأصناف الحالية وإعادة إدراج أصناف جديدة بالترميز الصحيح.",
                    "إعادة تعيين قائمة الطعام",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        RestaurantManagement.Data.DatabaseInitializer.ResetMenuItems();
                        MessageBox.Show("تم إعادة تعيين قائمة الطعام بنجاح!", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في إعادة تعيين قائمة الطعام: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            };

            resetAllButton.Click += (s, args) =>
            {
                var result = MessageBox.Show(
                    "تحذير: هذا سيحذف جميع البيانات (الفروع، الموظفين، قائمة الطعام، الطلبات) وإعادة إدراج البيانات الأولية بالترميز العربي الصحيح.\n\nهل أنت متأكد؟",
                    "إعادة تعيين جميع البيانات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        RestaurantManagement.Data.DatabaseInitializer.ResetAllData();
                        MessageBox.Show("تم إعادة تعيين جميع البيانات بنجاح!\n\nيرجى إعادة تشغيل البرنامج.", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Application.Exit();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في إعادة تعيين البيانات: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            };

            closeButton.Click += (s, args) => settingsForm.Close();

            settingsForm.Controls.Add(resetMenuButton);
            settingsForm.Controls.Add(resetAllButton);
            settingsForm.Controls.Add(closeButton);

            settingsForm.ShowDialog();
        }
        private void ExitMenuItem_Click(object? sender, EventArgs e) => Application.Exit();

        private void NewOrderMenuItem_Click(object? sender, EventArgs e)
        {
            var orderForm = new OrderForm(currentEmployee, currentBranch);
            orderForm.ShowDialog();
        }

        private void ViewOrdersMenuItem_Click(object? sender, EventArgs e)
        {
            var ordersListForm = new OrdersListForm(currentEmployee, currentBranch);
            ordersListForm.ShowDialog();
        }

        private void OrderStatusMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();

        private void ManageMenuMenuItem_Click(object? sender, EventArgs e)
        {
            var menuManagementForm = new MenuManagementForm(currentEmployee, currentBranch);
            menuManagementForm.ShowDialog();
        }

        private void CategoriesMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();

        private void ViewTablesMenuItem_Click(object? sender, EventArgs e)
        {
            var tablesForm = new TablesForm(currentEmployee, currentBranch);
            tablesForm.ShowDialog();
        }

        private void ReservationsMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void ManageInventoryMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void StockTransactionsMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void InventoryReportMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void ManageEmployeesMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void ShiftsMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void SalesReportMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void ProfitReportMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void EmployeePerformanceMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void ManageBranchesMenuItem_Click(object? sender, EventArgs e)
        {
            var branchManagementForm = new BranchManagementForm(currentEmployee, currentBranch);
            branchManagementForm.ShowDialog();
        }
        private void SwitchBranchMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void AboutMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();
        private void UserGuideMenuItem_Click(object? sender, EventArgs e) => ShowNotImplemented();

        private void ShowNotImplemented()
        {
            MessageBox.Show("هذه الميزة قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}