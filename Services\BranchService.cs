using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Services
{
    public class BranchService
    {
        public List<Branch> GetAllBranches()
        {
            var branches = new List<Branch>();
            
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                
                string query = "SELECT Id, Name, Address, Phone, Email, IsActive, CreatedDate FROM Branches ORDER BY Name";
                using var command = new SqlCommand(query, connection);
                using var reader = command.ExecuteReader();
                
                while (reader.Read())
                {
                    branches.Add(new Branch
                    {
                        Id = reader.GetInt32(0), // Id
                        Name = reader.GetString(1), // Name
                        Address = reader.IsDBNull(2) ? "" : reader.GetString(2), // Address
                        Phone = reader.IsDBNull(3) ? "" : reader.GetString(3), // Phone
                        Email = reader.IsDBNull(4) ? "" : reader.GetString(4), // Email
                        IsActive = reader.GetBoolean(5), // IsActive
                        CreatedDate = reader.GetDateTime(6) // CreatedDate
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetAllBranches: {ex.Message}");
            }
            
            return branches;
        }

        public Branch? GetBranchById(int id)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                
                string query = "SELECT Id, Name, Address, Phone, Email, IsActive, CreatedDate FROM Branches WHERE Id = @Id";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                using var reader = command.ExecuteReader();
                
                if (reader.Read())
                {
                    return new Branch
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Address = reader.IsDBNull(2) ? "" : reader.GetString(2),
                        Phone = reader.IsDBNull(3) ? "" : reader.GetString(3),
                        Email = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        IsActive = reader.GetBoolean(5),
                        CreatedDate = reader.GetDateTime(6)
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetBranchById: {ex.Message}");
            }
            
            return null;
        }

        public void AddBranch(Branch branch)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"INSERT INTO Branches (Name, Address, Phone, Email, IsActive, CreatedDate)
                               VALUES (@Name, @Address, @Phone, @Email, @IsActive, @CreatedDate)";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Name", branch.Name);
                command.Parameters.AddWithValue("@Address", branch.Address ?? "");
                command.Parameters.AddWithValue("@Phone", branch.Phone ?? "");
                command.Parameters.AddWithValue("@Email", branch.Email ?? "");
                command.Parameters.AddWithValue("@IsActive", branch.IsActive);
                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في AddBranch: {ex.Message}");
                throw;
            }
        }

        public void UpdateBranch(Branch branch)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"UPDATE Branches SET
                               Name = @Name, Address = @Address, Phone = @Phone,
                               Email = @Email, IsActive = @IsActive
                               WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", branch.Id);
                command.Parameters.AddWithValue("@Name", branch.Name);
                command.Parameters.AddWithValue("@Address", branch.Address ?? "");
                command.Parameters.AddWithValue("@Phone", branch.Phone ?? "");
                command.Parameters.AddWithValue("@Email", branch.Email ?? "");
                command.Parameters.AddWithValue("@IsActive", branch.IsActive);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateBranch: {ex.Message}");
                throw;
            }
        }

        public void DeleteBranch(int id)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // Check if branch has employees or orders
                string checkQuery = @"SELECT
                    (SELECT COUNT(*) FROM Employees WHERE BranchId = @Id) as EmployeeCount,
                    (SELECT COUNT(*) FROM Orders WHERE BranchId = @Id) as OrderCount";

                using var checkCommand = new SqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@Id", id);
                using var reader = checkCommand.ExecuteReader();

                if (reader.Read())
                {
                    int employeeCount = reader.GetInt32(0);
                    int orderCount = reader.GetInt32(1);

                    if (employeeCount > 0 || orderCount > 0)
                    {
                        throw new InvalidOperationException("لا يمكن حذف الفرع لأنه يحتوي على موظفين أو طلبات");
                    }
                }
                reader.Close();

                string deleteQuery = "DELETE FROM Branches WHERE Id = @Id";
                using var deleteCommand = new SqlCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@Id", id);
                deleteCommand.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في DeleteBranch: {ex.Message}");
                throw;
            }
        }
    }
}