<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>نظام إدارة المطعم</AssemblyTitle>
    <AssemblyDescription>نظام شامل لإدارة المطاعم مع دعم الفروع المتعددة</AssemblyDescription>
    <AssemblyCompany>Restaurant Management Systems</AssemblyCompany>
    <AssemblyProduct>Restaurant Management System</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <StartupObject>RestaurantManagement.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.4" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

</Project>