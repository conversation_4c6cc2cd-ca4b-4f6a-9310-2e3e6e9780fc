using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class MenuManagementForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private MenuService menuService;
        private List<MenuItem> menuItems;

        // Controls
        private ListView menuListView = null!;
        private ComboBox categoryFilterComboBox = null!;
        private TextBox searchTextBox = null!;
        private Button searchButton = null!;
        private Button addItemButton = null!;
        private Button editItemButton = null!;
        private Button deleteItemButton = null!;
        private Button refreshButton = null!;
        private Button closeButton = null!;
        private CheckBox availableOnlyCheckBox = null!;

        // Add/Edit controls
        private Panel editPanel = null!;
        private TextBox nameTextBox = null!;
        private TextBox descriptionTextBox = null!;
        private NumericUpDown priceNumeric = null!;
        private ComboBox categoryComboBox = null!;
        private CheckBox isAvailableCheckBox = null!;
        private Button saveButton = null!;
        private Button cancelEditButton = null!;
        private int editingItemId = 0;

        public MenuManagementForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            menuService = new MenuService();
            menuItems = new List<MenuItem>();
            
            InitializeComponent();
            LoadMenuItems();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "إدارة قائمة الطعام - Menu Management";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            // Search and filter section
            var searchLabel = new Label
            {
                Text = "البحث والتصفية:",
                Location = new Point(20, 20),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(searchLabel);

            var searchNameLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(20, 50),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(searchNameLabel);

            searchTextBox = new TextBox
            {
                Location = new Point(80, 50),
                Size = new Size(150, 23)
            };
            this.Controls.Add(searchTextBox);

            searchButton = new Button
            {
                Text = "بحث",
                Location = new Point(240, 50),
                Size = new Size(60, 25),
                BackColor = Color.Blue,
                ForeColor = Color.White
            };
            searchButton.Click += SearchButton_Click;
            this.Controls.Add(searchButton);

            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Location = new Point(320, 50),
                Size = new Size(40, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(categoryLabel);

            categoryFilterComboBox = new ComboBox
            {
                Location = new Point(370, 50),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryFilterComboBox.SelectedIndexChanged += FilterChanged;
            this.Controls.Add(categoryFilterComboBox);

            availableOnlyCheckBox = new CheckBox
            {
                Text = "المتوفر فقط",
                Location = new Point(510, 50),
                Size = new Size(100, 23),
                Checked = false
            };
            availableOnlyCheckBox.CheckedChanged += FilterChanged;
            this.Controls.Add(availableOnlyCheckBox);

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(630, 50),
                Size = new Size(80, 25),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            refreshButton.Click += RefreshButton_Click;
            this.Controls.Add(refreshButton);

            // Menu items list
            menuListView = new ListView
            {
                Location = new Point(20, 90),
                Size = new Size(950, 300),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false
            };
            menuListView.Columns.Add("الرقم", 60);
            menuListView.Columns.Add("اسم الصنف", 200);
            menuListView.Columns.Add("الوصف", 250);
            menuListView.Columns.Add("السعر", 80);
            menuListView.Columns.Add("الفئة", 120);
            menuListView.Columns.Add("متوفر", 80);
            menuListView.Columns.Add("تاريخ الإضافة", 120);
            this.Controls.Add(menuListView);

            // Action buttons
            addItemButton = new Button
            {
                Text = "إضافة صنف جديد",
                Location = new Point(20, 410),
                Size = new Size(120, 35),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            addItemButton.Click += AddItemButton_Click;
            this.Controls.Add(addItemButton);

            editItemButton = new Button
            {
                Text = "تعديل الصنف",
                Location = new Point(150, 410),
                Size = new Size(120, 35),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };
            editItemButton.Click += EditItemButton_Click;
            this.Controls.Add(editItemButton);

            deleteItemButton = new Button
            {
                Text = "حذف الصنف",
                Location = new Point(280, 410),
                Size = new Size(120, 35),
                BackColor = Color.Red,
                ForeColor = Color.White
            };
            deleteItemButton.Click += DeleteItemButton_Click;
            this.Controls.Add(deleteItemButton);

            closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(850, 410),
                Size = new Size(120, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);

            // Edit panel
            CreateEditPanel();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateEditPanel()
        {
            editPanel = new Panel
            {
                Location = new Point(20, 460),
                Size = new Size(950, 200),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            var editLabel = new Label
            {
                Text = "إضافة/تعديل صنف:",
                Location = new Point(10, 10),
                Size = new Size(120, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            editPanel.Controls.Add(editLabel);

            var nameLabel = new Label
            {
                Text = "اسم الصنف:",
                Location = new Point(10, 40),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(nameLabel);

            nameTextBox = new TextBox
            {
                Location = new Point(100, 40),
                Size = new Size(200, 23)
            };
            editPanel.Controls.Add(nameTextBox);

            var descLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(320, 40),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(descLabel);

            descriptionTextBox = new TextBox
            {
                Location = new Point(380, 40),
                Size = new Size(300, 23)
            };
            editPanel.Controls.Add(descriptionTextBox);

            var priceLabel = new Label
            {
                Text = "السعر:",
                Location = new Point(10, 80),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(priceLabel);

            priceNumeric = new NumericUpDown
            {
                Location = new Point(70, 80),
                Size = new Size(100, 23),
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 9999,
                Value = 0
            };
            editPanel.Controls.Add(priceNumeric);

            var catLabel = new Label
            {
                Text = "الفئة:",
                Location = new Point(190, 80),
                Size = new Size(40, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            editPanel.Controls.Add(catLabel);

            categoryComboBox = new ComboBox
            {
                Location = new Point(240, 80),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            editPanel.Controls.Add(categoryComboBox);

            isAvailableCheckBox = new CheckBox
            {
                Text = "متوفر",
                Location = new Point(410, 80),
                Size = new Size(80, 23),
                Checked = true
            };
            editPanel.Controls.Add(isAvailableCheckBox);

            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(100, 120),
                Size = new Size(100, 35),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            saveButton.Click += SaveButton_Click;
            editPanel.Controls.Add(saveButton);

            cancelEditButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(220, 120),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            cancelEditButton.Click += CancelEditButton_Click;
            editPanel.Controls.Add(cancelEditButton);

            this.Controls.Add(editPanel);
        }

        private void LoadMenuItems()
        {
            try
            {
                menuItems = menuService.GetAllMenuItems();
                LoadCategories();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الطعام: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCategories()
        {
            var categories = menuItems.Select(m => m.Category).Distinct().OrderBy(c => c).ToList();
            
            categoryFilterComboBox.Items.Clear();
            categoryFilterComboBox.Items.Add("الكل");
            categoryFilterComboBox.Items.AddRange(categories.ToArray());
            categoryFilterComboBox.SelectedIndex = 0;

            categoryComboBox.Items.Clear();
            categoryComboBox.Items.AddRange(categories.ToArray());
            if (categories.Count > 0)
                categoryComboBox.SelectedIndex = 0;
        }

        private void ApplyFilters()
        {
            var filteredItems = menuItems.AsEnumerable();

            // Filter by search text
            if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
            {
                string searchText = searchTextBox.Text.ToLower();
                filteredItems = filteredItems.Where(m => 
                    m.Name.ToLower().Contains(searchText) || 
                    m.Description.ToLower().Contains(searchText));
            }

            // Filter by category
            if (categoryFilterComboBox.SelectedIndex > 0)
            {
                string selectedCategory = categoryFilterComboBox.SelectedItem.ToString()!;
                filteredItems = filteredItems.Where(m => m.Category == selectedCategory);
            }

            // Filter by availability
            if (availableOnlyCheckBox.Checked)
            {
                filteredItems = filteredItems.Where(m => m.IsAvailable);
            }

            UpdateMenuList(filteredItems.ToList());
        }

        private void UpdateMenuList(List<MenuItem> filteredItems)
        {
            menuListView.Items.Clear();

            foreach (var item in filteredItems)
            {
                var listItem = new ListViewItem(item.Id.ToString());
                listItem.SubItems.Add(item.Name);
                listItem.SubItems.Add(item.Description);
                listItem.SubItems.Add(item.Price.ToString("F2"));
                listItem.SubItems.Add(item.Category);
                listItem.SubItems.Add(item.IsAvailable ? "نعم" : "لا");
                listItem.SubItems.Add(item.CreatedDate.ToString("dd/MM/yyyy"));
                listItem.Tag = item;

                if (!item.IsAvailable)
                {
                    listItem.BackColor = Color.LightGray;
                    listItem.ForeColor = Color.DarkGray;
                }

                menuListView.Items.Add(listItem);
            }
        }

        private void SearchButton_Click(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void FilterChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadMenuItems();
        }

        private void AddItemButton_Click(object? sender, EventArgs e)
        {
            editingItemId = 0;
            ClearEditForm();
            editPanel.Visible = true;
            nameTextBox.Focus();
        }

        private void EditItemButton_Click(object? sender, EventArgs e)
        {
            if (menuListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صنف للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = (MenuItem)menuListView.SelectedItems[0].Tag;
            editingItemId = selectedItem.Id;
            
            nameTextBox.Text = selectedItem.Name;
            descriptionTextBox.Text = selectedItem.Description;
            priceNumeric.Value = selectedItem.Price;
            categoryComboBox.Text = selectedItem.Category;
            isAvailableCheckBox.Checked = selectedItem.IsAvailable;
            
            editPanel.Visible = true;
            nameTextBox.Focus();
        }

        private void DeleteItemButton_Click(object? sender, EventArgs e)
        {
            if (menuListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = (MenuItem)menuListView.SelectedItems[0].Tag;
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف '{selectedItem.Name}'؟", 
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    menuService.DeleteMenuItem(selectedItem.Id);
                    MessageBox.Show("تم حذف الصنف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadMenuItems();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الصنف: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصنف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(categoryComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار أو إدخال فئة الصنف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var menuItem = new MenuItem
                {
                    Id = editingItemId,
                    Name = nameTextBox.Text.Trim(),
                    Description = descriptionTextBox.Text.Trim(),
                    Price = priceNumeric.Value,
                    Category = categoryComboBox.Text.Trim(),
                    IsAvailable = isAvailableCheckBox.Checked,
                    CreatedDate = editingItemId == 0 ? DateTime.Now : DateTime.Now // Will be handled by service
                };

                if (editingItemId == 0)
                {
                    menuService.AddMenuItem(menuItem);
                    MessageBox.Show("تم إضافة الصنف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    menuService.UpdateMenuItem(menuItem);
                    MessageBox.Show("تم تحديث الصنف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                editPanel.Visible = false;
                LoadMenuItems();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelEditButton_Click(object? sender, EventArgs e)
        {
            editPanel.Visible = false;
            ClearEditForm();
        }

        private void ClearEditForm()
        {
            nameTextBox.Clear();
            descriptionTextBox.Clear();
            priceNumeric.Value = 0;
            if (categoryComboBox.Items.Count > 0)
                categoryComboBox.SelectedIndex = 0;
            isAvailableCheckBox.Checked = true;
        }
    }
}
