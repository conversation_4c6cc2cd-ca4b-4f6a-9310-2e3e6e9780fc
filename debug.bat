@echo off
chcp 65001 >nul
cls
echo ========================================
echo    Restaurant Management System Debug
echo ========================================
echo.
echo Starting application with detailed output...
echo.

echo Checking .NET version...
dotnet --version
echo.

echo Checking project file...
if exist "RestaurantManagement.csproj" (
    echo ✓ Project file found
) else (
    echo ✗ Project file NOT found
    pause
    exit /b 1
)
echo.

echo Building application...
dotnet build RestaurantManagement.csproj --configuration Debug --verbosity normal
echo.

if %ERRORLEVEL% NEQ 0 (
    echo ✗ Build failed!
    pause
    exit /b 1
)

echo ✓ Build successful!
echo.
echo Starting application...
echo.

dotnet run --project RestaurantManagement.csproj --configuration Debug

echo.
echo Application closed with exit code: %ERRORLEVEL%
echo.
pause