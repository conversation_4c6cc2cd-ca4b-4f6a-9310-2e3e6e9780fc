using System;
using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public DateTime BirthDate { get; set; }
        public DateTime RegistrationDate { get; set; } = DateTime.Now;
        public int TotalOrders { get; set; }
        public decimal TotalSpent { get; set; }
        public string PreferredBranch { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public bool IsVIP { get; set; } = false;
        public decimal LoyaltyPoints { get; set; }
        public bool ReceivePromotions { get; set; } = true;
    }

    public class CustomerOrder
    {
        public int CustomerId { get; set; }
        public int OrderId { get; set; }
        public DateTime OrderDate { get; set; }
        public decimal OrderAmount { get; set; }
        public string BranchName { get; set; } = string.Empty;
    }

    public class LoyaltyTransaction
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal Points { get; set; }
        public string TransactionType { get; set; } = string.Empty; // Earned, Redeemed
        public string Description { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        public int? OrderId { get; set; }
    }
}