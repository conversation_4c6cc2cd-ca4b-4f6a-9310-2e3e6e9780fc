using System;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;
using RestaurantManagement.Forms;
using RestaurantManagement.Data;

namespace RestaurantManagement
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                // إعدادات أساسية للتطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // تعيين الثقافة العربية بشكل آمن
                try
                {
                    var arabicCulture = new CultureInfo("ar-EG");
                    Thread.CurrentThread.CurrentCulture = arabicCulture;
                    Thread.CurrentThread.CurrentUICulture = arabicCulture;
                }
                catch
                {
                    // استخدام الثقافة الافتراضية إذا فشل تعيين العربية
                }

                // إنشاء قاعدة البيانات والبيانات الأولية
                try
                {
                    DatabaseInitializer.InitializeDatabase();
                }
                catch (Exception dbEx)
                {
                    MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {dbEx.Message}", "خطأ قاعدة البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // تشغيل نموذج تسجيل الدخول العصري
                using (var loginForm = new RestaurantManagement.Forms.ModernLoginForm())
                {
                    var result = loginForm.ShowDialog();
                    if (result == DialogResult.OK)
                    {
                        // تشغيل النموذج الرئيسي
                        var mainForm = new MainForm();
                        
                        // تعيين المستخدم الحالي إذا كان متاحاً
                        try
                        {
                            if (loginForm.LoggedInEmployee != null && loginForm.SelectedBranch != null)
                            {
                                mainForm.SetCurrentUser(loginForm.LoggedInEmployee, loginForm.SelectedBranch);
                            }
                        }
                        catch
                        {
                            // تجاهل خطأ تعيين المستخدم
                        }
                        
                        Application.Run(mainForm);
                    }
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في تشغيل التطبيق:\n{ex.Message}";
                
                // إضافة تفاصيل إضافية إذا كانت متاحة
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                
                MessageBox.Show(errorMessage, "خطأ في التطبيق", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}