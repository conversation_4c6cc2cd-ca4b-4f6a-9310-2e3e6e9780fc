# 🗄️ إعداد قاعدة البيانات - نظام إدارة المطاعم

## 📋 خطوات الإعداد:

### 1️⃣ **تشغيل SQL Server:**
تأكد من تشغيل SQL Server على جهازك

### 2️⃣ **فتح SQL Server Management Studio (SSMS):**
- افتح SSMS
- اتصل بالخادم المحلي (localhost أو .)

### 3️⃣ **إنشاء قاعدة البيانات:**
```sql
-- انسخ والصق هذا الكود في SSMS واضغط F5
-- أو افتح ملف setup_database.sql وشغّله
```

#### الطريقة الأولى - تشغيل الملف:
1. في SSMS اضغط `File` → `Open` → `File`
2. اختر ملف `setup_database.sql`
3. اضغط `F5` أو `Execute`

#### الطريقة الثانية - نسخ ولصق:
انسخ محتوى ملف `setup_database.sql` والصقه في نافذة استعلام جديدة

### 4️⃣ **إدراج البيانات الأساسية:**
بعد إنشاء قاعدة البيانات:

1. افتح ملف `insert_sample_data.sql` في SSMS
2. اضغط `F5` لتشغيله

### ✅ **التحقق من النجاح:**

#### في SSMS شغّل هذا الاستعلام:
```sql
USE RestaurantManagement;
SELECT 'Branches' as TableName, COUNT(*) as RecordCount FROM Branches
UNION ALL
SELECT 'Employees', COUNT(*) FROM Employees
UNION ALL
SELECT 'MenuItems', COUNT(*) FROM MenuItems
UNION ALL
SELECT 'Tables', COUNT(*) FROM Tables;
```

#### النتيجة المتوقعة:
```
TableName    RecordCount
---------    -----------
Branches     3
Employees    4  
MenuItems    13
Tables       11
```

### 🎯 **بيانات تسجيل الدخول:**

#### المدير العام:
```
اسم المستخدم: admin
كلمة المرور: admin123
```

#### الكاشيرين:
```
اسم المستخدم: cashier1
كلمة المرور: cash123
```

### 🔧 **استكشاف الأخطاء:**

#### المشكلة: "Database does not exist"
**الحل:**
1. تأكد من تشغيل `setup_database.sql` أولاً
2. تحقق من اسم قاعدة البيانات: `RestaurantManagement`

#### المشكلة: "Invalid object name"
**الحل:**
1. تأكد من إنشاء جميع الجداول
2. شغّل `setup_database.sql` مرة أخرى

#### المشكلة: "Cannot insert duplicate key"
**الحل:**
- البيانات موجودة بالفعل، هذا طبيعي

### 📊 **هيكل قاعدة البيانات:**

#### الجداول الرئيسية:
- **Branches** - الفروع
- **Employees** - الموظفين  
- **MenuItems** - عناصر القائمة
- **Tables** - الطاولات
- **Orders** - الطلبات
- **OrderItems** - عناصر الطلبات
- **Customers** - العملاء

#### الجداول المساعدة:
- **BranchSettings** - إعدادات الفروع
- **Reservations** - الحجوزات
- **InventoryItems** - المخزون
- **StockTransactions** - حركات المخزون
- **Shifts** - المناوبات
- **LoyaltyTransactions** - نقاط الولاء

---
**بعد إتمام هذه الخطوات، ستكون قاعدة البيانات جاهزة للاستخدام! 🎉**