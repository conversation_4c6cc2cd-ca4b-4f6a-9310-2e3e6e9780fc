using System;
using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public enum OrderType
    {
        DineIn,     // تناول في المطعم
        Takeaway,   // تكاوي
        Delivery    // دليفري
    }

    public enum OrderStatus
    {
        Pending,    // في الانتظار
        Preparing,  // قيد التحضير
        Ready,      // جاهز
        Delivered,  // تم التسليم
        Cancelled   // ملغي
    }

    public class Order
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public int? TableNumber { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string CustomerEmail { get; set; } = string.Empty;
        public string CustomerAddress { get; set; } = string.Empty;
        public OrderType Type { get; set; }
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public decimal SubTotal { get; set; }
        public decimal Tax { get; set; }
        public decimal ServiceCharge { get; set; }
        public decimal DeliveryFee { get; set; }
        public decimal Discount { get; set; }
        public decimal TotalAmount { get; set; }
        public string Notes { get; set; } = string.Empty;
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، فيزا، ماستركارد، إلخ
        public bool IsPaid { get; set; } = false;
        public DateTime? DeliveryTime { get; set; }
        public string DeliveryDriverName { get; set; } = string.Empty;
        public List<OrderItem> Items { get; set; } = new List<OrderItem>();
    }

    public class OrderItem
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int MenuItemId { get; set; }
        public string MenuItemName { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public int Quantity { get; set; }
        public decimal Subtotal => (Price - DiscountAmount) * Quantity;
        public string Notes { get; set; } = string.Empty;
    }
}