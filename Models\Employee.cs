using System;

namespace RestaurantManagement.Models
{
    public enum EmployeeRole
    {
        Manager,    // مدير
        Cashier,    // كاشير
        Waiter,     // نادل
        Chef,       // طباخ
        Delivery    // دليفري
    }

    public class Employee
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public EmployeeRole Role { get; set; }
        public decimal Salary { get; set; }
        public DateTime HireDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool CanAccessAllBranches { get; set; } = false; // للمديرين العامين
    }

    public class Shift
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public decimal HoursWorked => EndTime.HasValue ? 
            (decimal)(EndTime.Value - StartTime).TotalHours : 0;
        public string Notes { get; set; } = string.Empty;
    }
}