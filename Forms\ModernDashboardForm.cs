using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class ModernDashboardForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private OrderService orderService;
        private InventoryService inventoryService;
        
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color SuccessColor = Color.FromArgb(39, 174, 96);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        public ModernDashboardForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            orderService = new OrderService();
            inventoryService = new InventoryService();
            
            InitializeComponent();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "لوحة التحكم الرئيسية";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = LightGray;
            this.WindowState = FormWindowState.Maximized;

            // Header Panel
            var headerPanel = CreateHeaderPanel();
            
            // Stats Cards Panel
            var statsPanel = CreateStatsPanel();
            
            // Charts Panel
            var chartsPanel = CreateChartsPanel();
            
            // Quick Actions Panel
            var actionsPanel = CreateQuickActionsPanel();
            
            // Recent Activities Panel
            var activitiesPanel = CreateRecentActivitiesPanel();

            // Main Layout
            var mainContainer = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                BackColor = LightGray
            };

            // Configure layout
            mainContainer.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainContainer.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainContainer.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F)); // Header
            mainContainer.RowStyles.Add(new RowStyle(SizeType.Absolute, 150F)); // Stats
            mainContainer.RowStyles.Add(new RowStyle(SizeType.Percent, 100F)); // Content

            // Add controls
            mainContainer.Controls.Add(headerPanel, 0, 0);
            mainContainer.SetColumnSpan(headerPanel, 2);
            
            mainContainer.Controls.Add(statsPanel, 0, 1);
            mainContainer.SetColumnSpan(statsPanel, 2);
            
            mainContainer.Controls.Add(chartsPanel, 0, 2);
            mainContainer.Controls.Add(actionsPanel, 1, 2);

            this.Controls.Add(mainContainer);
            this.ResumeLayout(false);
        }

        private Panel CreateHeaderPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = PrimaryColor,
                Margin = new Padding(0)
            };

            var titleLabel = new Label
            {
                Text = $"مرحباً {currentEmployee?.Name ?? "المستخدم"}",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 15),
                AutoSize = true
            };

            var branchLabel = new Label
            {
                Text = $"الفرع: {currentBranch?.Name ?? "غير محدد"}",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.White,
                Location = new Point(20, 45),
                AutoSize = true
            };

            var timeLabel = new Label
            {
                Text = DateTime.Now.ToString("dddd, dd MMMM yyyy - HH:mm"),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                Location = new Point(panel.Width - 300, 25),
                AutoSize = true
            };

            panel.Controls.AddRange(new Control[] { titleLabel, branchLabel, timeLabel });
            return panel;
        }

        private Panel CreateStatsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = LightGray,
                Padding = new Padding(20, 10, 20, 10)
            };

            // Create stat cards
            var todayOrdersCard = CreateStatCard("طلبات اليوم", "0", SuccessColor, "📋");
            var todayRevenueCard = CreateStatCard("إيرادات اليوم", "0 ج.م", AccentColor, "💰");
            var lowStockCard = CreateStatCard("مخزون منخفض", "0", WarningColor, "📦");
            var activeTablesCard = CreateStatCard("طاولات نشطة", "0", PrimaryColor, "🪑");

            // Layout cards
            var cardsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                BackColor = Color.Transparent
            };

            for (int i = 0; i < 4; i++)
            {
                cardsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            }

            cardsLayout.Controls.Add(todayOrdersCard, 0, 0);
            cardsLayout.Controls.Add(todayRevenueCard, 1, 0);
            cardsLayout.Controls.Add(lowStockCard, 2, 0);
            cardsLayout.Controls.Add(activeTablesCard, 3, 0);

            panel.Controls.Add(cardsLayout);
            return panel;
        }

        private Panel CreateStatCard(string title, string value, Color color, string icon)
        {
            var card = new Panel
            {
                BackColor = Color.White,
                Margin = new Padding(10),
                Size = new Size(250, 120)
            };

            // Add shadow effect
            card.Paint += (s, e) => DrawCardShadow(e.Graphics, card.ClientRectangle);

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = color,
                Location = new Point(20, 20),
                Size = new Size(50, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(80, 15),
                Size = new Size(150, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.Gray,
                Location = new Point(80, 50),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var colorBar = new Panel
            {
                BackColor = color,
                Location = new Point(0, 0),
                Size = new Size(5, card.Height),
                Dock = DockStyle.Left
            };

            card.Controls.AddRange(new Control[] { colorBar, iconLabel, valueLabel, titleLabel });
            return card;
        }

        private Panel CreateChartsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(20, 10, 10, 20)
            };

            // Add shadow effect
            panel.Paint += (s, e) => DrawCardShadow(e.Graphics, panel.ClientRectangle);

            var titleLabel = new Label
            {
                Text = "📊 إحصائيات المبيعات",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(20, 20),
                AutoSize = true
            };

            var chartPlaceholder = new Panel
            {
                BackColor = LightGray,
                Location = new Point(20, 60),
                Size = new Size(panel.Width - 40, panel.Height - 100),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            var chartLabel = new Label
            {
                Text = "سيتم إضافة الرسوم البيانية قريباً",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            chartPlaceholder.Controls.Add(chartLabel);
            panel.Controls.AddRange(new Control[] { titleLabel, chartPlaceholder });
            return panel;
        }

        private Panel CreateQuickActionsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(10, 10, 20, 20)
            };

            // Add shadow effect
            panel.Paint += (s, e) => DrawCardShadow(e.Graphics, panel.ClientRectangle);

            var titleLabel = new Label
            {
                Text = "⚡ إجراءات سريعة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(20, 20),
                AutoSize = true
            };

            // Quick action buttons
            var newOrderBtn = CreateModernButton("طلب جديد", SuccessColor, "🛎️");
            var viewOrdersBtn = CreateModernButton("عرض الطلبات", PrimaryColor, "📋");
            var manageTablesBtn = CreateModernButton("إدارة الطاولات", AccentColor, "🪑");
            var inventoryBtn = CreateModernButton("المخزون", WarningColor, "📦");
            var reportsBtn = CreateModernButton("التقارير", DangerColor, "📊");

            var buttonsLayout = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.TopDown,
                Location = new Point(20, 60),
                Size = new Size(panel.Width - 40, panel.Height - 80),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom,
                BackColor = Color.Transparent
            };

            buttonsLayout.Controls.AddRange(new Control[] { 
                newOrderBtn, viewOrdersBtn, manageTablesBtn, inventoryBtn, reportsBtn 
            });

            panel.Controls.AddRange(new Control[] { titleLabel, buttonsLayout });
            return panel;
        }

        private Panel CreateRecentActivitiesPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(20, 10, 20, 20)
            };

            // Add shadow effect
            panel.Paint += (s, e) => DrawCardShadow(e.Graphics, panel.ClientRectangle);

            var titleLabel = new Label
            {
                Text = "🕒 الأنشطة الأخيرة",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(20, 20),
                AutoSize = true
            };

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private Button CreateModernButton(string text, Color color, string icon)
        {
            var button = new Button
            {
                Text = $"{icon} {text}",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = color,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(200, 45),
                Margin = new Padding(0, 5, 0, 5),
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(15, 0, 0, 0)
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(color, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(color, 0.1f);

            // Add hover effect
            button.MouseEnter += (s, e) => button.BackColor = ControlPaint.Light(color, 0.1f);
            button.MouseLeave += (s, e) => button.BackColor = color;

            return button;
        }

        private void DrawCardShadow(Graphics g, Rectangle bounds)
        {
            // Create shadow effect
            var shadowBounds = new Rectangle(bounds.X + 3, bounds.Y + 3, bounds.Width, bounds.Height);
            using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
            {
                g.FillRectangle(shadowBrush, shadowBounds);
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                // Load real data here
                // This is a placeholder for now
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
