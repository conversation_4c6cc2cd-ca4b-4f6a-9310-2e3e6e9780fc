using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class ModernOrderForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private MenuService menuService;
        private OrderService orderService;
        
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color SuccessColor = Color.FromArgb(39, 174, 96);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        // Controls
        private Panel leftPanel;
        private Panel rightPanel;
        private FlowLayoutPanel menuItemsPanel;
        private ListView orderItemsList;
        private Label totalLabel;
        private ComboBox orderTypeCombo;
        private ComboBox tableCombo;
        private TextBox customerNameText;
        private TextBox customerPhoneText;
        private Button submitOrderButton;
        private Button clearOrderButton;

        private Order currentOrder;

        public ModernOrderForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            menuService = new MenuService();
            orderService = new OrderService();
            currentOrder = new Order();
            
            InitializeComponent();
            LoadMenuItems();
            LoadTables();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "طلب جديد - نظام إدارة المطعم";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = LightGray;

            CreateLeftPanel();
            CreateRightPanel();

            this.Controls.AddRange(new Control[] { leftPanel, rightPanel });
            this.ResumeLayout(false);
        }

        private void CreateLeftPanel()
        {
            leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 800,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = PrimaryColor
            };

            var titleLabel = new Label
            {
                Text = "🍽️ قائمة الطعام",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                AutoSize = true
            };

            headerPanel.Controls.Add(titleLabel);

            // Category filter
            var categoryPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = LightGray
            };

            var categoryButtons = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(10)
            };

            // Add category filter buttons
            var allCategoryBtn = CreateCategoryButton("الكل", true);
            var mainDishesBtn = CreateCategoryButton("أطباق رئيسية", false);
            var appetizersBtn = CreateCategoryButton("مقبلات", false);
            var drinksBtn = CreateCategoryButton("مشروبات", false);
            var dessertsBtn = CreateCategoryButton("حلويات", false);

            categoryButtons.Controls.AddRange(new Control[] { 
                allCategoryBtn, mainDishesBtn, appetizersBtn, drinksBtn, dessertsBtn 
            });

            categoryPanel.Controls.Add(categoryButtons);

            // Menu items panel
            menuItemsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(10),
                BackColor = Color.White
            };

            leftPanel.Controls.AddRange(new Control[] { headerPanel, categoryPanel, menuItemsPanel });
        }

        private void CreateRightPanel()
        {
            rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = AccentColor
            };

            var titleLabel = new Label
            {
                Text = "🛒 الطلب الحالي",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                AutoSize = true
            };

            headerPanel.Controls.Add(titleLabel);

            // Order details panel
            var detailsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 200,
                BackColor = LightGray,
                Padding = new Padding(15)
            };

            // Order type
            var orderTypeLabel = new Label
            {
                Text = "نوع الطلب:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(15, 15),
                Size = new Size(80, 25)
            };

            orderTypeCombo = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(100, 15),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            orderTypeCombo.Items.AddRange(new[] { "تناول في المطعم", "تكاوي", "دليفري" });
            orderTypeCombo.SelectedIndex = 0;

            // Table selection
            var tableLabel = new Label
            {
                Text = "الطاولة:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(15, 50),
                Size = new Size(80, 25)
            };

            tableCombo = new ComboBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(100, 50),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Customer info
            var customerLabel = new Label
            {
                Text = "اسم العميل:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(15, 85),
                Size = new Size(80, 25)
            };

            customerNameText = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(100, 85),
                Size = new Size(150, 25)
            };

            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(15, 120),
                Size = new Size(80, 25)
            };

            customerPhoneText = new TextBox
            {
                Font = new Font("Segoe UI", 10F),
                Location = new Point(100, 120),
                Size = new Size(150, 25)
            };

            detailsPanel.Controls.AddRange(new Control[] { 
                orderTypeLabel, orderTypeCombo, tableLabel, tableCombo,
                customerLabel, customerNameText, phoneLabel, customerPhoneText
            });

            // Order items list
            orderItemsList = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Segoe UI", 10F)
            };

            orderItemsList.Columns.AddRange(new ColumnHeader[]
            {
                new ColumnHeader { Text = "الصنف", Width = 150 },
                new ColumnHeader { Text = "الكمية", Width = 60 },
                new ColumnHeader { Text = "السعر", Width = 80 },
                new ColumnHeader { Text = "المجموع", Width = 80 }
            });

            // Bottom panel
            var bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 120,
                BackColor = LightGray,
                Padding = new Padding(15)
            };

            totalLabel = new Label
            {
                Text = "المجموع: 0.00 ج.م",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(15, 15),
                Size = new Size(200, 30)
            };

            submitOrderButton = new Button
            {
                Text = "✅ تأكيد الطلب",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = SuccessColor,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(15, 60),
                Size = new Size(120, 40)
            };
            submitOrderButton.FlatAppearance.BorderSize = 0;
            submitOrderButton.Click += SubmitOrderButton_Click;

            clearOrderButton = new Button
            {
                Text = "🗑️ مسح الطلب",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(231, 76, 60),
                FlatStyle = FlatStyle.Flat,
                Location = new Point(145, 60),
                Size = new Size(120, 40)
            };
            clearOrderButton.FlatAppearance.BorderSize = 0;
            clearOrderButton.Click += ClearOrderButton_Click;

            bottomPanel.Controls.AddRange(new Control[] { 
                totalLabel, submitOrderButton, clearOrderButton 
            });

            rightPanel.Controls.AddRange(new Control[] { 
                headerPanel, detailsPanel, orderItemsList, bottomPanel 
            });
        }

        private Button CreateCategoryButton(string text, bool isSelected)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = isSelected ? Color.White : DarkGray,
                BackColor = isSelected ? PrimaryColor : Color.White,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(120, 35),
                Margin = new Padding(5)
            };

            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = PrimaryColor;
            
            return button;
        }

        private Panel CreateMenuItemCard(MenuItem item)
        {
            var card = new Panel
            {
                Size = new Size(180, 220),
                BackColor = Color.White,
                Margin = new Padding(10),
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };

            // Item image placeholder
            var imagePanel = new Panel
            {
                Size = new Size(160, 100),
                Location = new Point(10, 10),
                BackColor = LightGray
            };

            var imageLabel = new Label
            {
                Text = "🍽️",
                Font = new Font("Segoe UI Emoji", 24F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            imagePanel.Controls.Add(imageLabel);

            // Item name
            var nameLabel = new Label
            {
                Text = item.Name,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(10, 120),
                Size = new Size(160, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Item description
            var descLabel = new Label
            {
                Text = item.Description,
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Gray,
                Location = new Point(10, 145),
                Size = new Size(160, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Item price
            var priceLabel = new Label
            {
                Text = $"{item.Price:C2}",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = AccentColor,
                Location = new Point(10, 180),
                Size = new Size(160, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Add button
            var addButton = new Button
            {
                Text = "إضافة",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = SuccessColor,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(60, 190),
                Size = new Size(60, 25)
            };
            addButton.FlatAppearance.BorderSize = 0;
            addButton.Click += (s, e) => AddItemToOrder(item);

            card.Controls.AddRange(new Control[] { 
                imagePanel, nameLabel, descLabel, priceLabel, addButton 
            });

            // Add hover effect
            card.MouseEnter += (s, e) => card.BackColor = Color.FromArgb(250, 250, 255);
            card.MouseLeave += (s, e) => card.BackColor = Color.White;

            return card;
        }

        private void LoadMenuItems()
        {
            try
            {
                var menuItems = menuService.GetAllMenuItems();
                menuItemsPanel.Controls.Clear();

                foreach (var item in menuItems.Where(i => i.IsAvailable))
                {
                    var card = CreateMenuItemCard(item);
                    menuItemsPanel.Controls.Add(card);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الطعام: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddItemToOrder(MenuItem item)
        {
            // Add item to current order
            var existingItem = orderItemsList.Items.Cast<ListViewItem>()
                .FirstOrDefault(i => i.Text == item.Name);

            if (existingItem != null)
            {
                // Increase quantity
                int quantity = int.Parse(existingItem.SubItems[1].Text) + 1;
                existingItem.SubItems[1].Text = quantity.ToString();
                existingItem.SubItems[3].Text = (quantity * item.Price).ToString("C2");
            }
            else
            {
                // Add new item
                var listItem = new ListViewItem(item.Name);
                listItem.SubItems.Add("1");
                listItem.SubItems.Add(item.Price.ToString("C2"));
                listItem.SubItems.Add(item.Price.ToString("C2"));
                listItem.Tag = item;
                orderItemsList.Items.Add(listItem);
            }

            UpdateTotal();
        }

        private void UpdateTotal()
        {
            decimal total = 0;
            foreach (ListViewItem item in orderItemsList.Items)
            {
                var menuItem = (MenuItem)item.Tag;
                int quantity = int.Parse(item.SubItems[1].Text);
                total += menuItem.Price * quantity;
            }

            totalLabel.Text = $"المجموع: {total:C2}";
        }

        private void SubmitOrderButton_Click(object sender, EventArgs e)
        {
            if (orderItemsList.Items.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للطلب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Create order object and save
                // This is a simplified implementation
                MessageBox.Show("تم تأكيد الطلب بنجاح!", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                ClearOrder();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تأكيد الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearOrderButton_Click(object sender, EventArgs e)
        {
            ClearOrder();
        }

        private void ClearOrder()
        {
            orderItemsList.Items.Clear();
            customerNameText.Clear();
            customerPhoneText.Clear();
            orderTypeCombo.SelectedIndex = 0;
            UpdateTotal();
        }

        private void LoadTables()
        {
            try
            {
                // Add table numbers 1-20
                for (int i = 1; i <= 20; i++)
                {
                    tableCombo.Items.Add($"طاولة {i}");
                }
                if (tableCombo.Items.Count > 0)
                    tableCombo.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطاولات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
