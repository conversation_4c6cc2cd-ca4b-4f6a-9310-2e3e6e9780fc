-- ========================================
-- نظام إدارة المطعم - إعداد قاعدة البيانات
-- Restaurant Management System - Database Setup
-- ========================================

-- إنشاء قاعدة البيانات مع دعم الترميز العربي
USE master;
GO

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'RestaurantManagement')
BEGIN
    CREATE DATABASE RestaurantManagement
    COLLATE Arabic_CI_AS;
    PRINT N'تم إنشاء قاعدة البيانات RestaurantManagement بنجاح';
END
ELSE
BEGIN
    PRINT N'قاعدة البيانات RestaurantManagement موجودة بالفعل';
END
GO

USE RestaurantManagement;
GO

-- ========================================
-- إنشاء الجداول
-- ========================================

-- جدول الفروع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
BEGIN
    CREATE TABLE Branches (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Address NVARCHAR(500) NOT NULL,
        Phone NVARCHAR(20),
        Email NVARCHAR(100),
        Manager NVARCHAR(100),
        IsActive BIT DEFAULT 1,
        OpeningTime TIME NOT NULL,
        ClosingTime TIME NOT NULL,
        DeliveryArea NVARCHAR(500),
        DeliveryRadius DECIMAL(5,2) DEFAULT 5.0,
        CreatedDate DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'تم إنشاء جدول الفروع';
END

-- جدول إعدادات الفروع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BranchSettings' AND xtype='U')
BEGIN
    CREATE TABLE BranchSettings (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        TaxRate DECIMAL(5,4) DEFAULT 0.14,
        ServiceCharge DECIMAL(5,4) DEFAULT 0.10,
        DeliveryFee DECIMAL(10,2) DEFAULT 15.00,
        MinimumOrderForDelivery DECIMAL(10,2) DEFAULT 50.00,
        AcceptOnlineOrders BIT DEFAULT 1,
        AcceptReservations BIT DEFAULT 1,
        MaxReservationDays INT DEFAULT 30,
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    PRINT 'تم إنشاء جدول إعدادات الفروع';
END

-- جدول الموظفين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
BEGIN
    CREATE TABLE Employees (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        Name NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(20),
        Email NVARCHAR(100),
        Role INT NOT NULL, -- 0=Manager, 1=Cashier, 2=Waiter, 3=Chef, 4=Delivery
        Salary DECIMAL(10,2),
        HireDate DATETIME2 NOT NULL,
        IsActive BIT DEFAULT 1,
        Username NVARCHAR(50) UNIQUE,
        Password NVARCHAR(255),
        CanAccessAllBranches BIT DEFAULT 0,
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    PRINT 'تم إنشاء جدول الموظفين';
END

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(20) UNIQUE NOT NULL,
        Email NVARCHAR(100),
        Address NVARCHAR(500),
        BirthDate DATE,
        RegistrationDate DATETIME2 DEFAULT GETDATE(),
        TotalOrders INT DEFAULT 0,
        TotalSpent DECIMAL(10,2) DEFAULT 0,
        PreferredBranch NVARCHAR(100),
        Notes NVARCHAR(500),
        IsVIP BIT DEFAULT 0,
        LoyaltyPoints DECIMAL(10,2) DEFAULT 0,
        ReceivePromotions BIT DEFAULT 1
    );
    PRINT 'تم إنشاء جدول العملاء';
END

-- جدول قائمة الطعام
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MenuItems' AND xtype='U')
BEGIN
    CREATE TABLE MenuItems (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500),
        Price DECIMAL(10,2) NOT NULL,
        Category NVARCHAR(50) NOT NULL,
        IsAvailable BIT DEFAULT 1,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        ImagePath NVARCHAR(500)
    );
    PRINT 'تم إنشاء جدول قائمة الطعام';
END

-- جدول الطاولات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Tables' AND xtype='U')
BEGIN
    CREATE TABLE Tables (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        Number INT NOT NULL,
        Capacity INT NOT NULL,
        Status INT DEFAULT 0, -- 0=Available, 1=Occupied, 2=Reserved, 3=Cleaning
        Location NVARCHAR(100),
        ReservationTime DATETIME2,
        ReservedBy NVARCHAR(100),
        ReservationPhone NVARCHAR(20),
        QRCode NVARCHAR(100),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        UNIQUE(BranchId, Number)
    );
    PRINT 'تم إنشاء جدول الطاولات';
END

-- جدول الطلبات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Orders' AND xtype='U')
BEGIN
    CREATE TABLE Orders (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        TableNumber INT,
        CustomerId INT,
        CustomerName NVARCHAR(100),
        CustomerPhone NVARCHAR(20),
        CustomerEmail NVARCHAR(100),
        CustomerAddress NVARCHAR(500),
        Type INT NOT NULL, -- 0=DineIn, 1=Takeaway, 2=Delivery
        Status INT DEFAULT 0, -- 0=Pending, 1=Preparing, 2=Ready, 3=Delivered, 4=Cancelled
        OrderDate DATETIME2 DEFAULT GETDATE(),
        SubTotal DECIMAL(10,2) NOT NULL,
        Tax DECIMAL(10,2) DEFAULT 0,
        ServiceCharge DECIMAL(10,2) DEFAULT 0,
        DeliveryFee DECIMAL(10,2) DEFAULT 0,
        Discount DECIMAL(10,2) DEFAULT 0,
        TotalAmount DECIMAL(10,2) NOT NULL,
        Notes NVARCHAR(500),
        EmployeeId INT NOT NULL,
        PaymentMethod NVARCHAR(50) DEFAULT N'نقدي',
        IsPaid BIT DEFAULT 0,
        DeliveryTime DATETIME2,
        DeliveryDriverName NVARCHAR(100),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
        FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
    );
    PRINT 'تم إنشاء جدول الطلبات';
END

-- جدول عناصر الطلبات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U')
BEGIN
    CREATE TABLE OrderItems (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrderId INT NOT NULL,
        MenuItemId INT NOT NULL,
        MenuItemName NVARCHAR(100) NOT NULL,
        Price DECIMAL(10,2) NOT NULL,
        Quantity INT NOT NULL,
        Notes NVARCHAR(500),
        FOREIGN KEY (OrderId) REFERENCES Orders(Id),
        FOREIGN KEY (MenuItemId) REFERENCES MenuItems(Id)
    );
    PRINT 'تم إنشاء جدول عناصر الطلبات';
END

-- جدول الحجوزات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Reservations' AND xtype='U')
BEGIN
    CREATE TABLE Reservations (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        TableId INT NOT NULL,
        TableNumber INT NOT NULL,
        CustomerName NVARCHAR(100) NOT NULL,
        CustomerPhone NVARCHAR(20) NOT NULL,
        CustomerEmail NVARCHAR(100),
        ReservationDate DATE NOT NULL,
        ReservationTime TIME NOT NULL,
        PartySize INT NOT NULL,
        Notes NVARCHAR(500),
        IsConfirmed BIT DEFAULT 0,
        SendReminder BIT DEFAULT 1,
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (TableId) REFERENCES Tables(Id)
    );
    PRINT 'تم إنشاء جدول الحجوزات';
END

-- جدول المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='InventoryItems' AND xtype='U')
BEGIN
    CREATE TABLE InventoryItems (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500),
        Category NVARCHAR(50) NOT NULL,
        Unit NVARCHAR(20) NOT NULL,
        CurrentStock DECIMAL(10,3) NOT NULL,
        MinimumStock DECIMAL(10,3) NOT NULL,
        MaximumStock DECIMAL(10,3) NOT NULL,
        UnitCost DECIMAL(10,2) NOT NULL,
        Supplier NVARCHAR(100),
        SupplierPhone NVARCHAR(20),
        ExpiryDate DATE,
        Barcode NVARCHAR(50),
        LastUpdated DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id)
    );
    PRINT 'تم إنشاء جدول المخزون';
END

-- جدول حركات المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockTransactions' AND xtype='U')
BEGIN
    CREATE TABLE StockTransactions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        InventoryItemId INT NOT NULL,
        ItemName NVARCHAR(100) NOT NULL,
        Quantity DECIMAL(10,3) NOT NULL,
        TransactionType NVARCHAR(20) NOT NULL, -- Purchase, Usage, Adjustment, Transfer
        UnitCost DECIMAL(10,2) NOT NULL,
        Notes NVARCHAR(500),
        InvoiceNumber NVARCHAR(50),
        TransferToBranchId INT,
        TransactionDate DATETIME2 DEFAULT GETDATE(),
        EmployeeId INT NOT NULL,
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (InventoryItemId) REFERENCES InventoryItems(Id),
        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id),
        FOREIGN KEY (TransferToBranchId) REFERENCES Branches(Id)
    );
    PRINT 'تم إنشاء جدول حركات المخزون';
END

-- جدول المناوبات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Shifts' AND xtype='U')
BEGIN
    CREATE TABLE Shifts (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        BranchId INT NOT NULL,
        EmployeeId INT NOT NULL,
        StartTime DATETIME2 NOT NULL,
        EndTime DATETIME2,
        Notes NVARCHAR(500),
        FOREIGN KEY (BranchId) REFERENCES Branches(Id),
        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
    );
    PRINT 'تم إنشاء جدول المناوبات';
END

-- جدول نقاط الولاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LoyaltyTransactions' AND xtype='U')
BEGIN
    CREATE TABLE LoyaltyTransactions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        CustomerId INT NOT NULL,
        Points DECIMAL(10,2) NOT NULL,
        TransactionType NVARCHAR(20) NOT NULL, -- Earned, Redeemed
        Description NVARCHAR(200),
        OrderId INT,
        TransactionDate DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
        FOREIGN KEY (OrderId) REFERENCES Orders(Id)
    );
    PRINT 'تم إنشاء جدول نقاط الولاء';
END

-- ========================================
-- إدراج البيانات الافتراضية
-- ========================================

-- التحقق من وجود بيانات افتراضية
IF NOT EXISTS (SELECT * FROM Branches)
BEGIN
    PRINT 'جاري إدراج البيانات الافتراضية...';
    
    -- إدراج فرع افتراضي
    INSERT INTO Branches (Name, Address, Phone, Email, Manager, OpeningTime, ClosingTime, DeliveryArea)
    VALUES (N'الفرع الرئيسي', N'شارع الجمهورية، القاهرة', '01000000000', '<EMAIL>', 
            N'أحمد محمد', '08:00:00', '23:00:00', N'القاهرة الجديدة، مدينة نصر، الزمالك');
    
    DECLARE @BranchId INT = SCOPE_IDENTITY();
    
    -- إدراج إعدادات الفرع
    INSERT INTO BranchSettings (BranchId) VALUES (@BranchId);
    
    -- إدراج موظف افتراضي (مدير)
    INSERT INTO Employees (BranchId, Name, Phone, Email, Role, Salary, HireDate, Username, Password, CanAccessAllBranches)
    VALUES (@BranchId, N'مدير النظام', '01000000000', '<EMAIL>', 0, 8000, GETDATE(), 'admin', 'admin123', 1);
    
    -- إدراج طاولات افتراضية (25 طاولة)
    DECLARE @i INT = 1;
    WHILE @i <= 25
    BEGIN
        INSERT INTO Tables (BranchId, Number, Capacity, Location, QRCode)
        VALUES (@BranchId, @i, 
                CASE WHEN @i <= 10 THEN 2 WHEN @i <= 20 THEN 4 ELSE 6 END,
                CASE WHEN @i <= 12 THEN N'الطابق الأول' ELSE N'الطابق الثاني' END,
                'TABLE_' + CAST(@BranchId AS NVARCHAR) + '_' + CAST(@i AS NVARCHAR));
        SET @i = @i + 1;
    END
    
    -- إدراج قائمة طعام افتراضية
    INSERT INTO MenuItems (Name, Description, Price, Category) VALUES
    (N'برجر لحم كلاسيك', N'برجر لحم بقري مشوي مع الخضار والصوص الخاص', 55.00, N'وجبات رئيسية'),
    (N'برجر دجاج مشوي', N'برجر دجاج مشوي مع الخس والطماطم', 45.00, N'وجبات رئيسية'),
    (N'بيتزا مارجريتا', N'بيتزا بالجبن الموتزاريلا والطماطم والريحان', 65.00, N'بيتزا'),
    (N'بيتزا بيبروني', N'بيتزا بالبيبروني والجبن والفلفل الحار', 75.00, N'بيتزا'),
    (N'بيتزا الخضار', N'بيتزا بالخضار المشكلة والجبن', 60.00, N'بيتزا'),
    (N'سلطة يونانية', N'سلطة خضار طازجة مع الجبن الأبيض والزيتون', 35.00, N'سلطات'),
    (N'سلطة سيزر', N'سلطة الخس مع صوص السيزر والدجاج المشوي', 40.00, N'سلطات'),
    (N'شوربة الدجاج', N'شوربة دجاج ساخنة بالخضار', 25.00, N'شوربات'),
    (N'شوربة العدس', N'شوربة عدس أحمر بالخضار والتوابل', 20.00, N'شوربات'),
    (N'عصير برتقال طازج', N'عصير برتقال طبيعي 100%', 18.00, N'مشروبات طبيعية'),
    (N'عصير مانجو', N'عصير مانجو طازج', 20.00, N'مشروبات طبيعية'),
    (N'كوكا كولا', N'مشروب غازي - علبة 330 مل', 12.00, N'مشروبات غازية'),
    (N'بيبسي', N'مشروب غازي - علبة 330 مل', 12.00, N'مشروبات غازية'),
    (N'شاي أحمر', N'شاي أحمر مع السكر', 10.00, N'مشروبات ساخنة'),
    (N'قهوة تركية', N'قهوة تركية أصيلة', 15.00, N'مشروبات ساخنة'),
    (N'كابتشينو', N'قهوة كابتشينو بالحليب', 25.00, N'مشروبات ساخنة'),
    (N'تشيز كيك', N'قطعة تشيز كيك بالفراولة', 30.00, N'حلويات'),
    (N'تيراميسو', N'حلوى تيراميسو إيطالية', 35.00, N'حلويات');
    
    -- إدراج عناصر مخزون افتراضية
    INSERT INTO InventoryItems (BranchId, Name, Description, Category, Unit, CurrentStock, MinimumStock, MaximumStock, UnitCost, Supplier, SupplierPhone, ExpiryDate) VALUES
    (@BranchId, N'لحم بقري', N'لحم بقري طازج للبرجر', N'لحوم', N'كيلو', 50.0, 10.0, 100.0, 120.00, N'مزرعة الأهرام', '01111111111', '2024-12-31'),
    (@BranchId, N'دجاج مجمد', N'قطع دجاج مجمدة', N'لحوم', N'كيلو', 30.0, 5.0, 80.0, 45.00, N'شركة الدواجن المصرية', '01222222222', '2024-06-30'),
    (@BranchId, N'جبن موتزاريلا', N'جبن موتزاريلا للبيتزا', N'ألبان', N'كيلو', 20.0, 3.0, 50.0, 85.00, N'مصنع الألبان', '01333333333', '2024-03-15'),
    (@BranchId, N'طماطم', N'طماطم طازجة', N'خضروات', N'كيلو', 25.0, 5.0, 60.0, 8.00, N'سوق الخضار', '01444444444', '2024-02-10'),
    (@BranchId, N'خس', N'خس طازج للسلطات', N'خضروات', N'كيلو', 15.0, 3.0, 40.0, 12.00, N'سوق الخضار', '01444444444', '2024-02-05'),
    (@BranchId, N'كوكا كولا', N'علب كوكا كولا 330 مل', N'مشروبات', N'علبة', 100.0, 20.0, 200.0, 8.00, N'شركة المشروبات', '01555555555', '2025-12-31');
    
    PRINT 'تم إدراج البيانات الافتراضية بنجاح';
END
ELSE
BEGIN
    PRINT 'البيانات الافتراضية موجودة بالفعل';
END

-- ========================================
-- إنشاء الفهارس لتحسين الأداء
-- ========================================

-- فهارس جدول الطلبات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Orders_BranchId_OrderDate')
    CREATE INDEX IX_Orders_BranchId_OrderDate ON Orders (BranchId, OrderDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Orders_Status')
    CREATE INDEX IX_Orders_Status ON Orders (Status);

-- فهارس جدول عناصر الطلبات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_OrderItems_OrderId')
    CREATE INDEX IX_OrderItems_OrderId ON OrderItems (OrderId);

-- فهارس جدول المخزون
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_InventoryItems_BranchId')
    CREATE INDEX IX_InventoryItems_BranchId ON InventoryItems (BranchId);

-- فهارس جدول الطاولات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Tables_BranchId_Status')
    CREATE INDEX IX_Tables_BranchId_Status ON Tables (BranchId, Status);

PRINT 'تم إنشاء الفهارس بنجاح';

-- ========================================
-- إنشاء Views مفيدة
-- ========================================

-- عرض الطلبات مع تفاصيل الفرع والموظف
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_OrdersWithDetails')
BEGIN
    EXEC('CREATE VIEW vw_OrdersWithDetails AS
    SELECT 
        o.*,
        b.Name as BranchName,
        e.Name as EmployeeName,
        c.Name as CustomerName
    FROM Orders o
    INNER JOIN Branches b ON o.BranchId = b.Id
    INNER JOIN Employees e ON o.EmployeeId = e.Id
    LEFT JOIN Customers c ON o.CustomerId = c.Id');
    
    PRINT 'تم إنشاء عرض الطلبات مع التفاصيل';
END

-- عرض المخزون المنخفض
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_LowStockItems')
BEGIN
    EXEC('CREATE VIEW vw_LowStockItems AS
    SELECT 
        i.*,
        b.Name as BranchName,
        CASE WHEN i.CurrentStock <= i.MinimumStock THEN 1 ELSE 0 END as IsLowStock,
        CASE WHEN i.ExpiryDate <= DATEADD(day, 7, GETDATE()) THEN 1 ELSE 0 END as IsExpiringSoon
    FROM InventoryItems i
    INNER JOIN Branches b ON i.BranchId = b.Id
    WHERE i.CurrentStock <= i.MinimumStock OR i.ExpiryDate <= DATEADD(day, 7, GETDATE())');
    
    PRINT 'تم إنشاء عرض المخزون المنخفض';
END

-- ========================================
-- إنشاء Stored Procedures مفيدة
-- ========================================

-- إجراء لحساب إجمالي المبيعات لفترة معينة
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetSalesReport')
BEGIN
    EXEC('CREATE PROCEDURE sp_GetSalesReport
        @BranchId INT = NULL,
        @FromDate DATE,
        @ToDate DATE
    AS
    BEGIN
        SELECT 
            b.Name as BranchName,
            COUNT(o.Id) as TotalOrders,
            SUM(o.SubTotal) as SubTotal,
            SUM(o.Tax) as TotalTax,
            SUM(o.ServiceCharge) as TotalServiceCharge,
            SUM(o.DeliveryFee) as TotalDeliveryFee,
            SUM(o.TotalAmount) as TotalSales,
            AVG(o.TotalAmount) as AverageOrderValue
        FROM Orders o
        INNER JOIN Branches b ON o.BranchId = b.Id
        WHERE o.OrderDate BETWEEN @FromDate AND DATEADD(day, 1, @ToDate)
        AND o.Status != 4 -- Exclude cancelled orders
        AND (@BranchId IS NULL OR o.BranchId = @BranchId)
        GROUP BY b.Id, b.Name
        ORDER BY TotalSales DESC
    END');
    
    PRINT 'تم إنشاء إجراء تقرير المبيعات';
END

PRINT '========================================';
PRINT 'تم إعداد قاعدة البيانات بنجاح!';
PRINT '';
PRINT 'بيانات تسجيل الدخول الافتراضية:';
PRINT 'اسم المستخدم: admin';
PRINT 'كلمة المرور: admin123';
PRINT '========================================';

GO