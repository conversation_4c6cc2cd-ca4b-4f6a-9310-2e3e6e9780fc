# تعيين الترميز العربي
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   نظام إدارة المطعم - Restaurant Management" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "جاري تشغيل النظام..." -ForegroundColor Yellow
Write-Host ""

# التحقق من وجود .NET 6.0
try {
    $dotnetVersion = dotnet --version
    Write-Host "إصدار .NET المثبت: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "خطأ: .NET 6.0 غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تحميل وتثبيت .NET 6.0 Runtime من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Blue
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود ملف المشروع
if (-not (Test-Path "RestaurantManagement.csproj")) {
    Write-Host "خطأ: ملف المشروع RestaurantManagement.csproj غير موجود" -ForegroundColor Red
    Write-Host "تأكد من وجود الملف في المجلد الحالي" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# استعادة الحزم
Write-Host "جاري استعادة الحزم..." -ForegroundColor Yellow
try {
    dotnet restore RestaurantManagement.csproj
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في استعادة الحزم"
    }
    Write-Host "تم استعادة الحزم بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في استعادة الحزم: $_" -ForegroundColor Red
    Write-Host "تأكد من اتصال الإنترنت وأن .NET 6.0 مثبت بشكل صحيح" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# بناء المشروع
Write-Host "جاري بناء المشروع..." -ForegroundColor Yellow
try {
    dotnet build RestaurantManagement.csproj --configuration Release
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في بناء المشروع"
    }
    Write-Host "تم بناء المشروع بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في بناء المشروع: $_" -ForegroundColor Red
    Write-Host "راجع رسائل الخطأ أعلاه لمعرفة السبب" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# عرض معلومات تسجيل الدخول
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "بيانات تسجيل الدخول الافتراضية:" -ForegroundColor Green
Write-Host "الفرع: الفرع الرئيسي" -ForegroundColor White
Write-Host "اسم المستخدم: admin" -ForegroundColor White
Write-Host "كلمة المرور: admin123" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# تشغيل التطبيق
Write-Host "جاري تشغيل التطبيق..." -ForegroundColor Yellow
try {
    dotnet run --project RestaurantManagement.csproj --configuration Release
} catch {
    Write-Host "خطأ في تشغيل التطبيق: $_" -ForegroundColor Red
}

Read-Host "اضغط Enter للخروج"