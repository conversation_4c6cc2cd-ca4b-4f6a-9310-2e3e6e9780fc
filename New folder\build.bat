@echo off
chcp 65001 >nul
cls
echo ========================================
echo        Restaurant Management System
echo ========================================
echo.
echo Building the application...
echo.

dotnet build RestaurantManagement.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo You can now run the application using:
    echo   run.bat
    echo   or
    echo   dotnet run
    echo.
) else (
    echo.
    echo ========================================
    echo Build failed! Check the errors above.
    echo ========================================
    echo.
)

pause