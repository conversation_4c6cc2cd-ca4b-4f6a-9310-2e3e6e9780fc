using System;
using System.Drawing;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class InventoryItemForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private InventoryItem? currentItem;
        private InventoryService inventoryService;
        private bool isEditMode;

        // Controls
        private TextBox nameTextBox = null!;
        private TextBox descriptionTextBox = null!;
        private ComboBox categoryComboBox = null!;
        private ComboBox unitComboBox = null!;
        private NumericUpDown currentStockNumeric = null!;
        private NumericUpDown minimumStockNumeric = null!;
        private NumericUpDown maximumStockNumeric = null!;
        private NumericUpDown unitCostNumeric = null!;
        private TextBox supplierTextBox = null!;
        private TextBox supplierPhoneTextBox = null!;
        private DateTimePicker expiryDatePicker = null!;
        private TextBox barcodeTextBox = null!;
        private Button saveButton = null!;
        private Button cancelButton = null!;

        public InventoryItemForm(Employee? employee, Branch? branch, InventoryItem? item = null)
        {
            currentEmployee = employee;
            currentBranch = branch;
            currentItem = item;
            inventoryService = new InventoryService();
            isEditMode = item != null;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings
            this.Text = isEditMode ? "تعديل صنف المخزون" : "إضافة صنف جديد";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            int yPos = 20;
            int labelWidth = 120;
            int controlWidth = 300;
            int spacing = 35;

            // Name
            var nameLabel = new Label
            {
                Text = "اسم الصنف:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            nameTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };

            yPos += spacing;

            // Description
            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            descriptionTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            yPos += 70;

            // Category
            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            categoryComboBox = new ComboBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };
            categoryComboBox.Items.AddRange(new[] { "خضار", "فواكه", "لحوم", "دواجن", "أسماك", "منتجات ألبان", "حبوب", "توابل", "مشروبات", "مواد تنظيف", "أخرى" });

            yPos += spacing;

            // Unit
            var unitLabel = new Label
            {
                Text = "الوحدة:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            unitComboBox = new ComboBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };
            unitComboBox.Items.AddRange(new[] { "كيلو", "جرام", "لتر", "مليلتر", "قطعة", "علبة", "كيس", "زجاجة", "عبوة" });

            yPos += spacing;

            // Current Stock
            var currentStockLabel = new Label
            {
                Text = "المخزون الحالي:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            currentStockNumeric = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Minimum Stock
            var minimumStockLabel = new Label
            {
                Text = "الحد الأدنى:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            minimumStockNumeric = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Maximum Stock
            var maximumStockLabel = new Label
            {
                Text = "الحد الأقصى:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            maximumStockNumeric = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Unit Cost
            var unitCostLabel = new Label
            {
                Text = "سعر الوحدة:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            unitCostNumeric = new NumericUpDown
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23),
                DecimalPlaces = 2,
                Maximum = 999999,
                Minimum = 0
            };

            yPos += spacing;

            // Supplier
            var supplierLabel = new Label
            {
                Text = "المورد:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            supplierTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };

            yPos += spacing;

            // Supplier Phone
            var supplierPhoneLabel = new Label
            {
                Text = "هاتف المورد:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            supplierPhoneTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };

            yPos += spacing;

            // Expiry Date
            var expiryDateLabel = new Label
            {
                Text = "تاريخ الانتهاء:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            expiryDatePicker = new DateTimePicker
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(6)
            };

            yPos += spacing;

            // Barcode
            var barcodeLabel = new Label
            {
                Text = "الباركود:",
                Location = new Point(20, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            barcodeTextBox = new TextBox
            {
                Location = new Point(150, yPos),
                Size = new Size(controlWidth, 23)
            };

            yPos += 50;

            // Buttons
            saveButton = new Button
            {
                Text = isEditMode ? "حفظ التعديلات" : "إضافة",
                Location = new Point(150, yPos),
                Size = new Size(100, 30),
                BackColor = Color.Green,
                ForeColor = Color.White,
                DialogResult = DialogResult.OK
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(260, yPos),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                DialogResult = DialogResult.Cancel
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                nameLabel, nameTextBox,
                descriptionLabel, descriptionTextBox,
                categoryLabel, categoryComboBox,
                unitLabel, unitComboBox,
                currentStockLabel, currentStockNumeric,
                minimumStockLabel, minimumStockNumeric,
                maximumStockLabel, maximumStockNumeric,
                unitCostLabel, unitCostNumeric,
                supplierLabel, supplierTextBox,
                supplierPhoneLabel, supplierPhoneTextBox,
                expiryDateLabel, expiryDatePicker,
                barcodeLabel, barcodeTextBox,
                saveButton, cancelButton
            });

            this.ResumeLayout(false);
        }

        private void LoadData()
        {
            if (isEditMode && currentItem != null)
            {
                nameTextBox.Text = currentItem.Name;
                descriptionTextBox.Text = currentItem.Description;
                categoryComboBox.Text = currentItem.Category;
                unitComboBox.Text = currentItem.Unit;
                currentStockNumeric.Value = currentItem.CurrentStock;
                minimumStockNumeric.Value = currentItem.MinimumStock;
                maximumStockNumeric.Value = currentItem.MaximumStock;
                unitCostNumeric.Value = currentItem.UnitCost;
                supplierTextBox.Text = currentItem.Supplier;
                supplierPhoneTextBox.Text = currentItem.SupplierPhone;
                expiryDatePicker.Value = currentItem.ExpiryDate;
                barcodeTextBox.Text = currentItem.Barcode;
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var item = new InventoryItem
                {
                    Id = currentItem?.Id ?? 0,
                    BranchId = currentBranch?.Id ?? 0,
                    Name = nameTextBox.Text.Trim(),
                    Description = descriptionTextBox.Text.Trim(),
                    Category = categoryComboBox.Text.Trim(),
                    Unit = unitComboBox.Text.Trim(),
                    CurrentStock = currentStockNumeric.Value,
                    MinimumStock = minimumStockNumeric.Value,
                    MaximumStock = maximumStockNumeric.Value,
                    UnitCost = unitCostNumeric.Value,
                    Supplier = supplierTextBox.Text.Trim(),
                    SupplierPhone = supplierPhoneTextBox.Text.Trim(),
                    ExpiryDate = expiryDatePicker.Value,
                    Barcode = barcodeTextBox.Text.Trim()
                };

                bool success;
                if (isEditMode)
                {
                    success = inventoryService.UpdateInventoryItem(item);
                }
                else
                {
                    success = inventoryService.AddInventoryItem(item);
                }

                if (success)
                {
                    MessageBox.Show(
                        isEditMode ? "تم تحديث الصنف بنجاح!" : "تم إضافة الصنف بنجاح!",
                        "نجح",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show(
                        isEditMode ? "فشل في تحديث الصنف!" : "فشل في إضافة الصنف!",
                        "خطأ",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصنف!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(categoryComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار فئة الصنف!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                categoryComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(unitComboBox.Text))
            {
                MessageBox.Show("يرجى اختيار وحدة القياس!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                unitComboBox.Focus();
                return false;
            }

            if (minimumStockNumeric.Value > maximumStockNumeric.Value)
            {
                MessageBox.Show("الحد الأدنى للمخزون لا يمكن أن يكون أكبر من الحد الأقصى!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                minimumStockNumeric.Focus();
                return false;
            }

            if (currentBranch == null)
            {
                MessageBox.Show("لم يتم تحديد الفرع!", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            return true;
        }
    }
}
