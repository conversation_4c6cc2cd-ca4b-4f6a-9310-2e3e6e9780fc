using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Models;
using RestaurantManagement.Services;
using RestaurantManagement.Data;

namespace RestaurantManagement.Forms
{
    public class HeldOrder
    {
        public int TableNumber { get; set; }
        public List<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public string WaiterName { get; set; } = "";
        public int OrderType { get; set; }
        public DateTime StartTime { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public partial class OrderForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private OrderService orderService;
        private MenuService menuService;
        private List<MenuItem> menuItems;
        private List<OrderItem> orderItems;
        private decimal totalAmount = 0;
        private decimal totalDiscount = 0;
        private Button totalDiscountButton = null!;
        private string selectedCategory = "";
        private int selectedTableNumber = 0;
        private DateTime orderStartTime = DateTime.Now;
        private static Dictionary<int, HeldOrder> heldOrders = new Dictionary<int, HeldOrder>();

        // Modern Touch UI Controls
        private Panel headerPanel = null!;
        private Panel categoriesPanel = null!;
        private Panel itemsPanel = null!;
        private Panel orderPanel = null!;
        private Panel footerPanel = null!;
        private Panel tablesPanel = null!;
        private Panel quantityPanel = null!;
        private Panel invoicePanel = null!;

        // Header controls
        private Button dineInButton = null!;
        private Button takeawayButton = null!;
        private Button deliveryButton = null!;
        private ComboBox tableComboBox = null!;
        private int selectedOrderType = 0; // 0=طاولات, 1=تكاوي, 2=دليفري
        private TextBox customerNameTextBox = null!;
        private TextBox customerPhoneTextBox = null!;
        private TextBox customerAddressTextBox = null!;
        private ComboBox waiterComboBox = null!;
        private ComboBox driversComboBox = null!;

        // Order summary controls
        private ListView orderListView = null!;
        private Label totalLabel = null!;
        private Label itemCountLabel = null!;

        // Action buttons
        private Button saveOrderButton = null!;
        private Button cancelButton = null!;
        private Button clearOrderButton = null!;
        private Button holdOrderButton = null!;
        private Button holdDeliveryButton = null!;
        private Button tablesButton = null!;
        private Button invoiceButton = null!;

        public OrderForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            orderService = new OrderService();
            menuService = new MenuService();
            menuItems = new List<MenuItem>();
            orderItems = new List<OrderItem>();
            
            InitializeComponent();
            LoadMenuItems();
            LoadTables();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings - Simple and Clear
            this.Text = "طلب جديد - New Order";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 10F);
            this.BackColor = Color.White;

            CreateSimpleLayout();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateSimpleLayout()
        {
            // Create a simple 3-section layout

            // Top section - Header (100px)
            headerPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(this.Width, 100),
                BackColor = Color.FromArgb(41, 128, 185),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(headerPanel);
            CreateSimpleHeader();

            // Left section - Categories (300px wide)
            categoriesPanel = new Panel
            {
                Location = new Point(0, 100),
                Size = new Size(300, this.Height - 200),
                BackColor = Color.FromArgb(52, 73, 94),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left,
                AutoScroll = true
            };
            this.Controls.Add(categoriesPanel);
            CreateSimpleCategories();

            // Right section - Order summary (450px wide - larger)
            orderPanel = new Panel
            {
                Location = new Point(this.Width - 450, 100),
                Size = new Size(450, this.Height - 200),
                BackColor = Color.FromArgb(236, 240, 241),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right
            };
            this.Controls.Add(orderPanel);
            CreateSimpleOrder();

            // Center section - Items and quantity
            var centerWidth = this.Width - 750; // 300 (left) + 450 (right)
            var centerPanel = new Panel
            {
                Location = new Point(300, 100),
                Size = new Size(centerWidth, this.Height - 200),
                BackColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(centerPanel);

            // Quantity panel (right side of center, 120px)
            quantityPanel = new Panel
            {
                Location = new Point(centerWidth - 120, 0),
                Size = new Size(120, centerPanel.Height),
                BackColor = Color.FromArgb(149, 165, 166),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right,
                Parent = centerPanel
            };
            centerPanel.Controls.Add(quantityPanel);
            CreateSimpleQuantity();

            // Items panel (remaining center space)
            itemsPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(centerWidth - 120, centerPanel.Height),
                BackColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                AutoScroll = true,
                Parent = centerPanel
            };
            centerPanel.Controls.Add(itemsPanel);

            // Bottom section - Footer (100px)
            footerPanel = new Panel
            {
                Location = new Point(0, this.Height - 100),
                Size = new Size(this.Width, 100),
                BackColor = Color.FromArgb(41, 128, 185),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(footerPanel);
            CreateSimpleFooter();
        }

        private void CreateSimpleHeader()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "نظام إدارة المطعم - طلب جديد",
                Location = new Point(20, 20),
                Size = new Size(400, 40),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleLeft
            };
            headerPanel.Controls.Add(titleLabel);

            // Order type buttons
            dineInButton = new Button
            {
                Text = "صالة",
                Location = new Point(450, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            dineInButton.FlatAppearance.BorderSize = 0;
            dineInButton.Click += (s, e) => SelectOrderType(0);
            headerPanel.Controls.Add(dineInButton);

            takeawayButton = new Button
            {
                Text = "📦 تكاوي",
                Location = new Point(560, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            takeawayButton.FlatAppearance.BorderSize = 0;
            takeawayButton.Click += (s, e) => SelectOrderType(1);
            headerPanel.Controls.Add(takeawayButton);

            deliveryButton = new Button
            {
                Text = "🚗 دليفري",
                Location = new Point(670, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            deliveryButton.FlatAppearance.BorderSize = 0;
            deliveryButton.Click += (s, e) => SelectOrderType(2);
            headerPanel.Controls.Add(deliveryButton);

            // Customer info
            customerNameTextBox = new TextBox
            {
                Location = new Point(450, 55),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12F),
                PlaceholderText = "اسم العميل",
                Visible = false // Hidden by default for dine-in
            };
            headerPanel.Controls.Add(customerNameTextBox);

            customerPhoneTextBox = new TextBox
            {
                Location = new Point(610, 55),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12F),
                PlaceholderText = "رقم الهاتف",
                Visible = false // Hidden by default for dine-in
            };
            customerPhoneTextBox.TextChanged += CustomerPhoneTextBox_TextChanged;
            headerPanel.Controls.Add(customerPhoneTextBox);

            customerAddressTextBox = new TextBox
            {
                Location = new Point(770, 55),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12F),
                PlaceholderText = "عنوان العميل",
                Visible = false // Hidden by default for dine-in
            };
            headerPanel.Controls.Add(customerAddressTextBox);

            // Waiter name (hidden)
            var waiterLabel = new Label
            {
                Text = "مقدم الطلبات:",
                Location = new Point(780, 55),
                Size = new Size(100, 30),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleLeft,
                Visible = false
            };
            headerPanel.Controls.Add(waiterLabel);

            waiterComboBox = new ComboBox
            {
                Location = new Point(890, 55),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Visible = false
            };

            // Add waiters list
            waiterComboBox.Items.AddRange(new[] {
                "أحمد محمد",
                "فاطمة علي",
                "محمد حسن",
                "نور الدين",
                "سارة أحمد",
                "عبدالله محمود",
                "مريم سالم",
                "يوسف إبراهيم"
            });
            waiterComboBox.SelectedIndex = 0;
            headerPanel.Controls.Add(waiterComboBox);

            // Drivers list (for delivery)
            var driversLabel = new Label
            {
                Text = "الطيار:",
                Location = new Point(780, 55),
                Size = new Size(60, 30),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleLeft,
                Visible = false
            };
            headerPanel.Controls.Add(driversLabel);

            driversComboBox = new ComboBox
            {
                Location = new Point(850, 55),
                Size = new Size(120, 30),
                Font = new Font("Segoe UI", 12F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Visible = false
            };
            driversComboBox.Items.AddRange(new[] {
                "كريم الطيار",
                "أحمد الدليفري",
                "محمد السريع",
                "علي التوصيل",
                "حسام الطيار",
                "يوسف الدليفري",
                "سامي السريع"
            });
            driversComboBox.SelectedIndex = 0;
            headerPanel.Controls.Add(driversComboBox);

            // Action buttons
            tablesButton = new Button
            {
                Text = "🪑 الطاولات",
                Location = new Point(780, 15),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            tablesButton.FlatAppearance.BorderSize = 0;
            tablesButton.Click += TablesButton_Click;
            headerPanel.Controls.Add(tablesButton);

            invoiceButton = new Button
            {
                Text = "🧾 الفاتورة",
                Location = new Point(910, 15),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            invoiceButton.FlatAppearance.BorderSize = 0;
            invoiceButton.Click += InvoiceButton_Click;
            headerPanel.Controls.Add(invoiceButton);

            // Table combo box (hidden for now)
            tableComboBox = new ComboBox
            {
                Location = new Point(500, 55),
                Size = new Size(100, 30),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 12F),
                Visible = false
            };
            headerPanel.Controls.Add(tableComboBox);

            // Close button
            var closeButton = new Button
            {
                Text = "✕",
                Location = new Point(headerPanel.Width - 60, 20),
                Size = new Size(40, 40),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();
            headerPanel.Controls.Add(closeButton);
        }

        private void SelectOrderType(int orderType)
        {
            selectedOrderType = orderType;

            // Reset all button colors
            dineInButton.BackColor = Color.FromArgb(149, 165, 166);
            takeawayButton.BackColor = Color.FromArgb(149, 165, 166);
            deliveryButton.BackColor = Color.FromArgb(149, 165, 166);

            // Highlight selected button
            switch (orderType)
            {
                case 0: // صالة
                    dineInButton.BackColor = Color.FromArgb(39, 174, 96);
                    customerNameTextBox.Visible = false;
                    customerPhoneTextBox.Visible = false;
                    customerAddressTextBox.Visible = false;
                    driversComboBox.Visible = false;
                    holdDeliveryButton.Visible = false;
                    customerNameTextBox.Clear();
                    customerPhoneTextBox.Clear();
                    customerAddressTextBox.Clear();
                    break;
                case 1: // تكاوي
                    takeawayButton.BackColor = Color.FromArgb(230, 126, 34);
                    customerNameTextBox.Visible = false;
                    customerPhoneTextBox.Visible = false;
                    customerAddressTextBox.Visible = false;
                    driversComboBox.Visible = false;
                    holdDeliveryButton.Visible = false;
                    customerNameTextBox.Clear();
                    customerPhoneTextBox.Clear();
                    customerAddressTextBox.Clear();
                    break;
                case 2: // دليفري
                    deliveryButton.BackColor = Color.FromArgb(52, 152, 219);
                    customerNameTextBox.Visible = true;
                    customerPhoneTextBox.Visible = true;
                    customerAddressTextBox.Visible = true;
                    driversComboBox.Visible = true;
                    holdDeliveryButton.Visible = true;
                    break;
            }
        }

        private void CreateSimpleCategories()
        {
            var titleLabel = new Label
            {
                Text = "فئات الأصناف",
                Location = new Point(20, 20),
                Size = new Size(260, 40),
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };
            categoriesPanel.Controls.Add(titleLabel);

            var categories = new[]
            {
                new { Name = "جميع الأصناف", Icon = "🍽️", Color = Color.FromArgb(52, 152, 219) },
                new { Name = "برجر", Icon = "🍔", Color = Color.FromArgb(230, 126, 34) },
                new { Name = "بيتزا", Icon = "🍕", Color = Color.FromArgb(231, 76, 60) },
                new { Name = "سلطات", Icon = "🥗", Color = Color.FromArgb(46, 204, 113) },
                new { Name = "مشروبات", Icon = "🥤", Color = Color.FromArgb(52, 152, 219) },
                new { Name = "مشروبات ساخنة", Icon = "☕", Color = Color.FromArgb(155, 89, 182) },
                new { Name = "حلويات", Icon = "🍰", Color = Color.FromArgb(241, 196, 15) },
                new { Name = "مشاوي", Icon = "🍖", Color = Color.FromArgb(192, 57, 43) },
                new { Name = "أطباق رئيسية", Icon = "🍛", Color = Color.FromArgb(39, 174, 96) },
                new { Name = "أطباق شعبية", Icon = "🍲", Color = Color.FromArgb(142, 68, 173) }
            };

            int y = 80;
            foreach (var category in categories)
            {
                var categoryButton = new Button
                {
                    Text = $"{category.Icon}\n{category.Name}",
                    Location = new Point(20, y),
                    Size = new Size(260, 60),
                    BackColor = category.Color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Cursor = Cursors.Hand,
                    Tag = category.Name == "جميع الأصناف" ? "" : category.Name
                };

                categoryButton.FlatAppearance.BorderSize = 0;
                categoryButton.FlatAppearance.MouseOverBackColor = ControlPaint.Light(category.Color, 0.3f);
                categoryButton.Click += CategoryButton_Click;

                categoriesPanel.Controls.Add(categoryButton);
                y += 70;
            }
        }



        private void CreateSimpleQuantity()
        {
            var titleLabel = new Label
            {
                Text = "الكمية",
                Location = new Point(10, 20),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };
            quantityPanel.Controls.Add(titleLabel);

            // Number buttons 1-10 vertically
            int y = 60;
            for (int i = 1; i <= 10; i++)
            {
                var numberButton = new Button
                {
                    Text = i.ToString(),
                    Location = new Point(25, y),
                    Size = new Size(70, 50),
                    BackColor = Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                    Cursor = Cursors.Hand,
                    Tag = i
                };
                numberButton.FlatAppearance.BorderSize = 0;
                numberButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(41, 128, 185);
                numberButton.Click += NumberButton_Click;
                quantityPanel.Controls.Add(numberButton);

                y += 60;
            }
        }

        private void CreateSimpleOrder()
        {
            var titleLabel = new Label
            {
                Text = "الطلب الحالي",
                Location = new Point(20, 20),
                Size = new Size(410, 40),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleCenter
            };
            orderPanel.Controls.Add(titleLabel);

            // Total info
            itemCountLabel = new Label
            {
                Text = "0 صنف",
                Location = new Point(20, 70),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 14F),
                ForeColor = Color.FromArgb(127, 140, 141)
            };
            orderPanel.Controls.Add(itemCountLabel);

            totalLabel = new Label
            {
                Text = "0.00 جنيه",
                Location = new Point(250, 70),
                Size = new Size(180, 30),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(39, 174, 96),
                TextAlign = ContentAlignment.MiddleRight
            };
            orderPanel.Controls.Add(totalLabel);

            // Order list - larger
            orderListView = new ListView
            {
                Location = new Point(20, 110),
                Size = new Size(410, 350),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                HeaderStyle = ColumnHeaderStyle.None,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                Font = new Font("Segoe UI", 11F)
            };
            orderListView.Columns.Add("الصنف", 280);
            orderListView.Columns.Add("خصم", 65);
            orderListView.Columns.Add("حذف", 65);
            orderListView.DoubleClick += OrderListView_DoubleClick;
            orderListView.ItemActivate += OrderListView_ItemActivate;
            orderListView.MouseClick += OrderListView_MouseClick;
            orderPanel.Controls.Add(orderListView);

            // Action buttons
            holdOrderButton = new Button
            {
                Text = "⏸️ تعليق الفاتورة",
                Location = new Point(20, 480),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(243, 156, 18),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            holdOrderButton.FlatAppearance.BorderSize = 0;
            holdOrderButton.Click += HoldOrderButton_Click;
            orderPanel.Controls.Add(holdOrderButton);

            holdDeliveryButton = new Button
            {
                Text = "🚚 تعليق دليفري",
                Location = new Point(180, 480),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Visible = false
            };
            holdDeliveryButton.FlatAppearance.BorderSize = 0;
            holdDeliveryButton.Click += HoldDeliveryButton_Click;
            orderPanel.Controls.Add(holdDeliveryButton);

            clearOrderButton = new Button
            {
                Text = "🗑️ مسح الطلب",
                Location = new Point(340, 480),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            clearOrderButton.FlatAppearance.BorderSize = 0;
            clearOrderButton.Click += ClearOrderButton_Click;
            orderPanel.Controls.Add(clearOrderButton);
        }

        private void CreateSimpleFooter()
        {
            // Invoice calculation section (left side)
            var calcPanel = new Panel
            {
                Location = new Point(20, 10),
                Size = new Size(600, 80),
                BackColor = Color.FromArgb(52, 73, 94),
                BorderStyle = BorderStyle.FixedSingle
            };
            footerPanel.Controls.Add(calcPanel);

            // Subtotal
            var subtotalLabel = new Label
            {
                Text = "المجموع الفرعي:",
                Location = new Point(10, 10),
                Size = new Size(120, 25),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold)
            };
            calcPanel.Controls.Add(subtotalLabel);

            var subtotalValueLabel = new Label
            {
                Text = "0.00 جنيه",
                Location = new Point(140, 10),
                Size = new Size(100, 25),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F),
                Name = "subtotalValue"
            };
            calcPanel.Controls.Add(subtotalValueLabel);

            // Discount
            var discountLabel = new Label
            {
                Text = "الخصم:",
                Location = new Point(260, 10),
                Size = new Size(60, 25),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold)
            };
            calcPanel.Controls.Add(discountLabel);

            var discountValueLabel = new Label
            {
                Text = "0.00 جنيه",
                Location = new Point(330, 10),
                Size = new Size(100, 25),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F),
                Name = "discountValue"
            };
            calcPanel.Controls.Add(discountValueLabel);

            // Total discount button
            totalDiscountButton = new Button
            {
                Text = "خصم على الإجمالي",
                Location = new Point(450, 8),
                Size = new Size(140, 30),
                BackColor = Color.FromArgb(243, 156, 18),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            totalDiscountButton.FlatAppearance.BorderSize = 0;
            totalDiscountButton.Click += TotalDiscountButton_Click;
            calcPanel.Controls.Add(totalDiscountButton);

            // Final total (large)
            var finalTotalLabel = new Label
            {
                Text = "الإجمالي النهائي:",
                Location = new Point(10, 45),
                Size = new Size(150, 30),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold)
            };
            calcPanel.Controls.Add(finalTotalLabel);

            var finalTotalValueLabel = new Label
            {
                Text = "0.00 جنيه",
                Location = new Point(170, 45),
                Size = new Size(200, 30),
                ForeColor = Color.FromArgb(46, 204, 113),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                Name = "finalTotalValue"
            };
            calcPanel.Controls.Add(finalTotalValueLabel);

            // Action buttons (right side)
            saveOrderButton = new Button
            {
                Text = "💾 حفظ الطلب",
                Location = new Point(650, 15),
                Size = new Size(180, 35),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            saveOrderButton.FlatAppearance.BorderSize = 0;
            saveOrderButton.Click += SaveOrderButton_Click;
            footerPanel.Controls.Add(saveOrderButton);

            cancelButton = new Button
            {
                Text = "❌ إلغاء",
                Location = new Point(850, 15),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Click += (s, e) => this.Close();
            footerPanel.Controls.Add(cancelButton);
        }

        private void CategoryButton_Click(object? sender, EventArgs e)
        {
            if (sender is Button button)
            {
                selectedCategory = button.Tag?.ToString() ?? "";

                // Update button appearance
                foreach (Control control in categoriesPanel.Controls)
                {
                    if (control is Button btn)
                    {
                        btn.FlatAppearance.BorderSize = btn == button ? 3 : 0;
                        btn.FlatAppearance.BorderColor = Color.White;
                    }
                }

                LoadMenuItems();
            }
        }

        private void DisplayMenuItems()
        {
            itemsPanel.Controls.Clear();

            var filteredItems = string.IsNullOrEmpty(selectedCategory)
                ? menuItems
                : menuItems.Where(m => m.Category == selectedCategory).ToList();

            if (filteredItems.Count == 0) return;

            // Medium card layout for better space usage
            int itemWidth = 180;
            int itemHeight = 220;
            int spacing = 15;
            int itemsPerRow = Math.Max(1, (itemsPanel.Width - 40) / (itemWidth + spacing));

            int x = 20, y = 20;

            for (int i = 0; i < filteredItems.Count; i++)
            {
                var item = filteredItems[i];
                CreateMediumMenuItemCard(item, x, y);

                x += itemWidth + spacing;
                if ((i + 1) % itemsPerRow == 0)
                {
                    x = 20;
                    y += itemHeight + spacing;
                }
            }
        }

        private void CreateMediumMenuItemCard(MenuItem item, int x, int y)
        {
            var cardPanel = new Panel
            {
                Location = new Point(x, y),
                Size = new Size(180, 220),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };
            cardPanel.Click += (s, e) => ShowInvoiceForItem(item);

            // Item image placeholder
            var imagePanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(160, 100),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var imageLabel = new Label
            {
                Text = GetItemIcon(item.Category),
                Font = new Font("Segoe UI", 36F),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                ForeColor = Color.FromArgb(149, 165, 166)
            };
            imagePanel.Controls.Add(imageLabel);
            cardPanel.Controls.Add(imagePanel);

            // Item name
            var nameLabel = new Label
            {
                Text = item.Name,
                Location = new Point(10, 120),
                Size = new Size(160, 40),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.TopCenter
            };
            cardPanel.Controls.Add(nameLabel);

            // Price
            var priceLabel = new Label
            {
                Text = $"{item.Price:F2} جنيه",
                Location = new Point(10, 170),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(39, 174, 96),
                TextAlign = ContentAlignment.MiddleLeft
            };
            cardPanel.Controls.Add(priceLabel);

            // Add button
            var addButton = new Button
            {
                Text = "إضافة",
                Location = new Point(115, 170),
                Size = new Size(55, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Tag = item
            };
            addButton.FlatAppearance.BorderSize = 0;
            addButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(41, 128, 185);
            addButton.Click += AddItemToOrder;
            cardPanel.Controls.Add(addButton);

            // Availability overlay
            if (!item.IsAvailable)
            {
                var overlayPanel = new Panel
                {
                    Location = new Point(0, 0),
                    Size = cardPanel.Size,
                    BackColor = Color.FromArgb(150, 0, 0, 0)
                };

                var unavailableLabel = new Label
                {
                    Text = "غير متوفر",
                    Dock = DockStyle.Fill,
                    Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                    ForeColor = Color.White,
                    TextAlign = ContentAlignment.MiddleCenter,
                    BackColor = Color.Transparent
                };
                overlayPanel.Controls.Add(unavailableLabel);
                cardPanel.Controls.Add(overlayPanel);
                overlayPanel.BringToFront();

                addButton.Enabled = false;
            }

            itemsPanel.Controls.Add(cardPanel);
        }

        private string GetItemIcon(string category)
        {
            return category switch
            {
                "برجر" => "🍔",
                "بيتزا" => "🍕",
                "سلطات" => "🥗",
                "مشروبات" => "🥤",
                "مشروبات ساخنة" => "☕",
                "حلويات" => "🍰",
                "مشاوي" => "🍖",
                "أطباق رئيسية" => "🍛",
                "أطباق شعبية" => "🍲",
                _ => "🍽️"
            };
        }

        private void LoadMenuItems()
        {
            try
            {
                menuItems = menuService.GetAllMenuItems();
                DisplayMenuItems();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الطعام: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddItemToOrder(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is MenuItem item)
            {
                // Check if item already exists in order
                var existingItem = orderItems.FirstOrDefault(oi => oi.MenuItemId == item.Id);

                if (existingItem != null)
                {
                    existingItem.Quantity++;
                }
                else
                {
                    var orderItem = new OrderItem
                    {
                        MenuItemId = item.Id,
                        MenuItemName = item.Name,
                        Price = item.Price,
                        OriginalPrice = item.Price,
                        DiscountAmount = 0,
                        Quantity = 1,
                        Notes = ""
                    };
                    orderItems.Add(orderItem);
                }

                UpdateOrderDisplay();

                // Visual feedback
                button.BackColor = Color.FromArgb(39, 174, 96);
                var timer = new System.Windows.Forms.Timer { Interval = 200 };
                timer.Tick += (s, args) =>
                {
                    button.BackColor = Color.FromArgb(52, 152, 219);
                    timer.Stop();
                    timer.Dispose();
                };
                timer.Start();
            }
        }

        private void UpdateOrderDisplay()
        {
            orderListView.Items.Clear();

            foreach (var item in orderItems)
            {
                var listItem = new ListViewItem();
                listItem.Text = CreateOrderItemText(item);
                listItem.SubItems.Add("خصم");
                listItem.SubItems.Add("❌");
                listItem.Tag = item;
                listItem.Font = new Font("Segoe UI", 11F);
                listItem.UseItemStyleForSubItems = false;

                // Discount button styling
                listItem.SubItems[1].BackColor = Color.FromArgb(243, 156, 18);
                listItem.SubItems[1].ForeColor = Color.White;
                listItem.SubItems[1].Font = new Font("Segoe UI", 10F, FontStyle.Bold);

                // Delete button styling
                listItem.SubItems[2].BackColor = Color.FromArgb(231, 76, 60);
                listItem.SubItems[2].ForeColor = Color.White;
                listItem.SubItems[2].Font = new Font("Segoe UI", 12F, FontStyle.Bold);

                orderListView.Items.Add(listItem);
            }

            UpdateTotal();
        }

        private string CreateOrderItemText(OrderItem item)
        {
            var text = $"{item.MenuItemName}\n";

            if (item.DiscountAmount > 0)
            {
                text += $"السعر: {item.OriginalPrice:F2} جنيه\n";
                text += $"خصم: {item.DiscountAmount:F2} جنيه\n";
                text += $"الكمية: {item.Quantity} × {(item.OriginalPrice - item.DiscountAmount):F2} = {item.Subtotal:F2} جنيه";
            }
            else
            {
                text += $"الكمية: {item.Quantity} × {item.Price:F2} = {(item.Quantity * item.Price):F2} جنيه";
            }

            if (!string.IsNullOrEmpty(item.Notes))
            {
                text += $"\nملاحظات: {item.Notes}";
            }

            return text;
        }

        private void ClearOrderButton_Click(object? sender, EventArgs e)
        {
            if (orderItems.Count == 0) return;

            var result = MessageBox.Show("هل أنت متأكد من مسح الطلب؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                orderItems.Clear();
                UpdateOrderDisplay();
            }
        }

        private void LoadTables()
        {
            // This will be implemented when we create the table service
            for (int i = 1; i <= 25; i++)
            {
                tableComboBox.Items.Add($"طاولة {i}");
            }
        }



        private void UpdateTotal()
        {
            // Calculate subtotal from current item prices (after individual discounts)
            var subtotal = orderItems.Sum(item => item.Subtotal);

            // Calculate final total after total discount
            var finalTotal = subtotal - totalDiscount;

            // Ensure final total is not negative
            finalTotal = Math.Max(0, finalTotal);

            // Update totalAmount and totalLabel with final total (after all discounts)
            totalAmount = finalTotal;
            totalLabel.Text = $"{finalTotal:F2} جنيه";

            itemCountLabel.Text = $"{orderItems.Sum(i => i.Quantity)} صنف";

            // Calculate individual discounts for display
            var itemsDiscount = orderItems.Sum(item => item.DiscountAmount * item.Quantity);

            // Update footer calculation with correct values
            UpdateFooterCalculation(subtotal, itemsDiscount, totalDiscount, finalTotal);

            // Update total discount button text
            UpdateTotalDiscountButtonText();
        }

        private void UpdateTotalDiscountButtonText()
        {
            if (totalDiscountButton != null)
            {
                if (totalDiscount > 0)
                {
                    totalDiscountButton.Text = $"خصم إجمالي: {totalDiscount:F2}";
                }
                else
                {
                    totalDiscountButton.Text = "خصم على الإجمالي";
                }
            }
        }

        private void UpdateFooterCalculation(decimal subtotal, decimal itemsDiscount, decimal totalDiscountAmount, decimal finalTotal)
        {
            // Find footer labels by name - search more thoroughly
            var subtotalLabel = FindControlByName(footerPanel, "subtotalValue") as Label;
            var discountLabel = FindControlByName(footerPanel, "discountValue") as Label;
            var finalTotalLabel = FindControlByName(footerPanel, "finalTotalValue") as Label;

            // المجموع الفرعي = إجمالي الأصناف بعد خصوماتها الفردية
            if (subtotalLabel != null)
            {
                subtotalLabel.Text = $"{subtotal:F2} جنيه";
                subtotalLabel.Invalidate(); // Force refresh
            }

            // إجمالي الخصومات = خصومات الأصناف + خصم الإجمالي
            var allDiscounts = itemsDiscount + totalDiscountAmount;
            if (discountLabel != null)
            {
                discountLabel.Text = $"{allDiscounts:F2} جنيه";
                discountLabel.Invalidate(); // Force refresh
            }

            // الإجمالي النهائي = المجموع الفرعي - خصم الإجمالي
            if (finalTotalLabel != null)
            {
                finalTotalLabel.Text = $"{finalTotal:F2} جنيه";
                finalTotalLabel.Invalidate(); // Force refresh
            }
        }

        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name) return parent;

            foreach (Control child in parent.Controls)
            {
                if (child.Name == name) return child;
                var found = FindControlByName(child, name);
                if (found != null) return found;
            }
            return null;
        }

        private void TotalDiscountButton_Click(object? sender, EventArgs e)
        {
            // If there's already a total discount, ask if user wants to remove it
            if (totalDiscount > 0)
            {
                var result = MessageBox.Show(
                    $"يوجد خصم إجمالي حالي: {totalDiscount:F2} جنيه\nهل تريد إزالته أم تعديله؟",
                    "خصم الإجمالي",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes) // Remove discount
                {
                    totalDiscount = 0;
                    UpdateTotal();
                    return;
                }
                else if (result == DialogResult.Cancel) // Cancel
                {
                    return;
                }
                // If No, continue to modify discount
            }

            var discountForm = new Form
            {
                Text = "خصم على الإجمالي",
                Size = new Size(350, 200),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var discountTypeCombo = new ComboBox
            {
                Location = new Point(20, 20),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10F)
            };
            discountTypeCombo.Items.AddRange(new[] { "مبلغ ثابت", "نسبة مئوية %" });
            discountTypeCombo.SelectedIndex = 0;
            discountForm.Controls.Add(discountTypeCombo);

            var valueTextBox = new TextBox
            {
                Location = new Point(160, 20),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10F),
                PlaceholderText = "القيمة"
            };
            discountForm.Controls.Add(valueTextBox);

            var applyButton = new Button
            {
                Text = "تطبيق",
                Location = new Point(20, 60),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            applyButton.FlatAppearance.BorderSize = 0;
            discountForm.Controls.Add(applyButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(120, 60),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Click += (s, args) => discountForm.Close();
            discountForm.Controls.Add(cancelButton);

            applyButton.Click += (s, args) =>
            {
                if (decimal.TryParse(valueTextBox.Text, out decimal discountValue))
                {
                    // Calculate current subtotal (after individual item discounts)
                    var currentSubtotal = orderItems.Sum(item => item.Subtotal);

                    if (discountTypeCombo.SelectedIndex == 0) // Fixed amount
                    {
                        totalDiscount = Math.Min(discountValue, currentSubtotal);
                    }
                    else // Percentage
                    {
                        totalDiscount = currentSubtotal * (discountValue / 100);
                    }

                    UpdateTotal();
                    discountForm.Close();
                }
                else
                {
                    MessageBox.Show("يرجى إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            discountForm.ShowDialog();
        }

        private void SaveOrderButton_Click(object? sender, EventArgs e)
        {
            if (orderItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة عناصر للطلب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            bool isDineIn = selectedOrderType == 0;
            if (!isDineIn && selectedOrderType == 2 && string.IsNullOrWhiteSpace(customerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل للدليفري", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerNameTextBox.Focus();
                return;
            }

            try
            {
                var order = new Order
                {
                    BranchId = currentBranch?.Id ?? 1,
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = customerPhoneTextBox.Text.Trim(),
                    CustomerAddress = customerAddressTextBox.Text.Trim(),
                    Type = (OrderType)selectedOrderType,
                    Status = OrderStatus.Pending,
                    OrderDate = DateTime.Now,
                    SubTotal = totalAmount,
                    Tax = totalAmount * 0.14m, // 14% tax
                    ServiceCharge = totalAmount * 0.10m, // 10% service charge
                    DeliveryFee = selectedOrderType == 2 ? 15.00m : 0, // Delivery fee
                    TotalAmount = totalAmount + (totalAmount * 0.14m) + (totalAmount * 0.10m) + (selectedOrderType == 2 ? 15.00m : 0),
                    Notes = selectedOrderType == 2 ? $"طيار الدليفري: {driversComboBox.Text}" : $"مقدم الطلبات: {waiterComboBox.Text}",
                    EmployeeId = currentEmployee?.Id ?? 1,
                    TableNumber = selectedOrderType == 0 ? (selectedTableNumber > 0 ? selectedTableNumber : 1) : null
                };

                int orderId = orderService.CreateOrder(order, orderItems);

                // Remove from held orders if it was held
                if (selectedTableNumber > 0 && heldOrders.ContainsKey(selectedTableNumber))
                {
                    heldOrders.Remove(selectedTableNumber);
                }

                // Clear held order from database and free table
                if (selectedTableNumber > 0)
                {
                    try
                    {
                        orderService.ClearHeldOrder(selectedTableNumber);
                        orderService.UpdateTableStatus(selectedTableNumber, TableStatus.Available);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"تحذير: خطأ في تحرير الطاولة: {ex.Message}", "تحذير",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                MessageBox.Show($"تم حفظ الطلب بنجاح\nرقم الطلب: {orderId}", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear the order but don't close the form
                ClearCurrentOrder();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private int SaveDeliveryOrder()
        {
            var order = new Order
            {
                BranchId = currentBranch?.Id ?? 1,
                CustomerName = customerNameTextBox.Text.Trim(),
                CustomerPhone = customerPhoneTextBox.Text.Trim(),
                CustomerAddress = customerAddressTextBox.Text.Trim(),
                Type = OrderType.Delivery,
                Status = OrderStatus.Pending,
                OrderDate = DateTime.Now,
                SubTotal = totalAmount,
                Tax = totalAmount * 0.14m, // 14% tax
                ServiceCharge = totalAmount * 0.10m, // 10% service charge
                DeliveryFee = 15.00m, // Delivery fee
                TotalAmount = totalAmount + (totalAmount * 0.14m) + (totalAmount * 0.10m) + 15.00m,
                Notes = $"طيار الدليفري: {driversComboBox.Text}",
                EmployeeId = currentEmployee?.Id ?? 1,
                DeliveryDriverName = driversComboBox.Text
            };

            var orderService = new OrderService();
            return orderService.CreateOrder(order, orderItems);
        }

        private void ClearCurrentOrder()
        {
            orderItems.Clear();
            totalDiscount = 0;
            selectedTableNumber = 0;
            customerNameTextBox.Clear();
            customerPhoneTextBox.Clear();
            customerAddressTextBox.Clear();
            customerNameTextBox.BackColor = Color.White;
            customerPhoneTextBox.BackColor = Color.White;
            customerAddressTextBox.BackColor = Color.White;
            waiterComboBox.SelectedIndex = 0;
            SelectOrderType(0); // Reset to dine-in
            UpdateOrderDisplay();
            UpdateTotalDiscountButtonText(); // Reset button text
        }

        private void OrderListView_DoubleClick(object? sender, EventArgs e)
        {
            if (orderListView.SelectedItems.Count == 0) return;

            var selectedOrderItem = (OrderItem)orderListView.SelectedItems[0].Tag;

            // Create quantity dialog
            var quantityForm = new Form
            {
                Text = "تعديل الكمية",
                Size = new Size(300, 200),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var itemLabel = new Label
            {
                Text = selectedOrderItem.MenuItemName,
                Location = new Point(20, 20),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter
            };
            quantityForm.Controls.Add(itemLabel);

            var quantityLabel = new Label
            {
                Text = "الكمية:",
                Location = new Point(20, 60),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            quantityForm.Controls.Add(quantityLabel);

            var quantityNumeric = new NumericUpDown
            {
                Location = new Point(90, 60),
                Size = new Size(80, 25),
                Minimum = 1,
                Maximum = 99,
                Value = selectedOrderItem.Quantity,
                Font = new Font("Segoe UI", 11F)
            };
            quantityForm.Controls.Add(quantityNumeric);

            var removeButton = new Button
            {
                Text = "حذف",
                Location = new Point(50, 100),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            removeButton.Click += (s, args) =>
            {
                orderItems.Remove(selectedOrderItem);
                UpdateOrderDisplay();
                quantityForm.Close();
            };
            quantityForm.Controls.Add(removeButton);

            var updateButton = new Button
            {
                Text = "تحديث",
                Location = new Point(140, 100),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            updateButton.Click += (s, args) =>
            {
                selectedOrderItem.Quantity = (int)quantityNumeric.Value;
                UpdateOrderDisplay();
                quantityForm.Close();
            };
            quantityForm.Controls.Add(updateButton);

            quantityForm.ShowDialog();
        }

        private void TablesButton_Click(object? sender, EventArgs e)
        {
            ShowTablesPanel();
        }

        private void InvoiceButton_Click(object? sender, EventArgs e)
        {
            ShowInvoicePanel();
        }

        private void ShowInvoiceForItem(MenuItem item)
        {
            ShowInvoicePanel();
        }

        private void HoldOrderButton_Click(object? sender, EventArgs e)
        {
            if (orderItems.Count == 0)
            {
                MessageBox.Show("لا يوجد أصناف في الطلب لتعليقه", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedTableNumber == 0)
            {
                MessageBox.Show("يرجى اختيار طاولة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل تريد تعليق هذه الفاتورة؟", "تعليق الفاتورة",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // Save held order
                var heldOrder = new HeldOrder
                {
                    TableNumber = selectedTableNumber,
                    OrderItems = new List<OrderItem>(orderItems.Select(item => new OrderItem
                    {
                        MenuItemId = item.MenuItemId,
                        MenuItemName = item.MenuItemName,
                        Price = item.Price,
                        OriginalPrice = item.OriginalPrice,
                        DiscountAmount = item.DiscountAmount,
                        Quantity = item.Quantity,
                        Notes = item.Notes
                    })),
                    CustomerName = customerNameTextBox.Text,
                    CustomerPhone = customerPhoneTextBox.Text,
                    WaiterName = waiterComboBox.Text,
                    OrderType = selectedOrderType,
                    StartTime = orderStartTime,
                    TotalAmount = totalAmount
                };

                heldOrders[selectedTableNumber] = heldOrder;

                // Save to database and update table status
                try
                {
                    var orderService = new OrderService();
                    orderService.SaveHeldOrder(selectedTableNumber, orderItems);
                    orderService.UpdateTableStatus(selectedTableNumber, TableStatus.Occupied);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الطلب المعلق: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                MessageBox.Show($"تم تعليق الفاتورة للطاولة {selectedTableNumber} بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear current order
                ClearCurrentOrder();
            }
        }

        private void HoldDeliveryButton_Click(object? sender, EventArgs e)
        {
            if (orderItems.Count == 0)
            {
                MessageBox.Show("لا يوجد أصناف في الطلب لتعليقه", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedOrderType != 2)
            {
                MessageBox.Show("تعليق الدليفري متاح فقط للطلبات من نوع دليفري", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(customerPhoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم هاتف العميل أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerPhoneTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(customerAddressTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان العميل أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerAddressTextBox.Focus();
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد تعليق طلب الدليفري للعميل {customerNameTextBox.Text}؟\n" +
                $"الطيار: {driversComboBox.Text}\n" +
                $"العنوان: {customerAddressTextBox.Text}",
                "تعليق دليفري",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    // Save delivery order to database
                    var orderService = new OrderService();
                    var deliveryId = SaveDeliveryOrder();

                    MessageBox.Show($"تم تعليق طلب الدليفري بنجاح\nرقم الطلب: {deliveryId}", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Clear current order
                    ClearCurrentOrder();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ طلب الدليفري: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void NumberButton_Click(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is int quantity)
            {
                if (orderListView.SelectedItems.Count > 0)
                {
                    var selectedOrderItem = (OrderItem)orderListView.SelectedItems[0].Tag;
                    selectedOrderItem.Quantity = quantity;
                    UpdateOrderDisplay();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار صنف من الفاتورة أولاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void OrderListView_ItemActivate(object? sender, EventArgs e)
        {
            if (orderListView.SelectedItems.Count > 0)
            {
                var selectedOrderItem = (OrderItem)orderListView.SelectedItems[0].Tag;
                ShowNotesDialog(selectedOrderItem);
            }
        }

        private void OrderListView_MouseClick(object? sender, MouseEventArgs e)
        {
            var hitTest = orderListView.HitTest(e.Location);
            if (hitTest.Item != null && hitTest.SubItem != null)
            {
                var columnIndex = hitTest.Item.SubItems.IndexOf(hitTest.SubItem);
                var selectedOrderItem = (OrderItem)hitTest.Item.Tag;

                // Check if clicked on discount column (second column)
                if (columnIndex == 1)
                {
                    ShowDiscountDialog(selectedOrderItem);
                }
                // Check if clicked on delete column (third column)
                else if (columnIndex == 2)
                {
                    var result = MessageBox.Show(
                        $"هل تريد حذف '{selectedOrderItem.MenuItemName}' من الطلب؟",
                        "تأكيد الحذف",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button2);

                    if (result == DialogResult.Yes)
                    {
                        orderItems.Remove(selectedOrderItem);
                        UpdateOrderDisplay();
                    }
                }
            }
        }

        private void ShowDiscountDialog(OrderItem orderItem)
        {
            var discountForm = new Form
            {
                Text = "خصم الصنف",
                Size = new Size(350, 250),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var itemLabel = new Label
            {
                Text = orderItem.MenuItemName,
                Location = new Point(20, 20),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter
            };
            discountForm.Controls.Add(itemLabel);

            var discountLabel = new Label
            {
                Text = "نوع الخصم:",
                Location = new Point(20, 60),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            discountForm.Controls.Add(discountLabel);

            var discountTypeCombo = new ComboBox
            {
                Location = new Point(110, 60),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            discountTypeCombo.Items.AddRange(new[] { "مبلغ ثابت", "نسبة مئوية" });
            discountTypeCombo.SelectedIndex = 0;
            discountForm.Controls.Add(discountTypeCombo);

            var valueLabel = new Label
            {
                Text = "القيمة:",
                Location = new Point(20, 100),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            discountForm.Controls.Add(valueLabel);

            var valueTextBox = new TextBox
            {
                Location = new Point(80, 100),
                Size = new Size(80, 25),
                PlaceholderText = "10"
            };
            discountForm.Controls.Add(valueTextBox);

            var unitLabel = new Label
            {
                Text = "%",
                Location = new Point(170, 100),
                Size = new Size(20, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            discountForm.Controls.Add(unitLabel);

            discountTypeCombo.SelectedIndexChanged += (s, args) =>
            {
                unitLabel.Text = discountTypeCombo.SelectedIndex == 0 ? "%" : "جنيه";
            };

            // Quick discount buttons
            var quickDiscounts = new[] { "5", "10", "15", "20", "25", "50" };
            int x = 20, y = 140;
            foreach (var discount in quickDiscounts)
            {
                var discountButton = new Button
                {
                    Text = discount + "%",
                    Location = new Point(x, y),
                    Size = new Size(40, 30),
                    BackColor = Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 8F),
                    Cursor = Cursors.Hand
                };
                discountButton.Click += (s, args) =>
                {
                    valueTextBox.Text = discount;
                    discountTypeCombo.SelectedIndex = 0;
                };
                discountForm.Controls.Add(discountButton);

                x += 45;
            }

            var applyButton = new Button
            {
                Text = "تطبيق",
                Location = new Point(150, 180),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            applyButton.Click += (s, args) =>
            {
                if (decimal.TryParse(valueTextBox.Text, out decimal discountValue))
                {
                    // Set original price if not set
                    if (orderItem.OriginalPrice == 0)
                    {
                        orderItem.OriginalPrice = orderItem.Price;
                    }

                    if (discountTypeCombo.SelectedIndex == 0) // Fixed amount
                    {
                        orderItem.DiscountAmount = Math.Min(discountValue, orderItem.OriginalPrice);
                    }
                    else // Percentage
                    {
                        orderItem.DiscountAmount = orderItem.OriginalPrice * (discountValue / 100);
                    }

                    // Ensure discount doesn't exceed original price
                    orderItem.DiscountAmount = Math.Min(orderItem.DiscountAmount, orderItem.OriginalPrice);

                    UpdateOrderDisplay();
                    discountForm.Close();
                }
                else
                {
                    MessageBox.Show("يرجى إدخال قيمة صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };
            discountForm.Controls.Add(applyButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(240, 180),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, args) => discountForm.Close();
            discountForm.Controls.Add(cancelButton);

            discountForm.ShowDialog();
        }

        private void ShowNotesDialog(OrderItem orderItem)
        {
            var notesForm = new Form
            {
                Text = "ملاحظات الصنف",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var itemLabel = new Label
            {
                Text = orderItem.MenuItemName,
                Location = new Point(20, 20),
                Size = new Size(350, 30),
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter
            };
            notesForm.Controls.Add(itemLabel);

            var notesLabel = new Label
            {
                Text = "ملاحظات خاصة:",
                Location = new Point(20, 60),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            notesForm.Controls.Add(notesLabel);

            var notesTextBox = new TextBox
            {
                Location = new Point(20, 90),
                Size = new Size(350, 100),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Text = orderItem.Notes,
                PlaceholderText = "مثال: بدون شطة، بدون كاتشب، إضافة جبن..."
            };
            notesForm.Controls.Add(notesTextBox);

            // Quick notes buttons
            var quickNotes = new[] { "بدون شطة", "بدون كاتشب", "إضافة جبن", "بدون بصل", "مشوي جيداً", "قليل الملح" };
            int x = 20, y = 200;
            foreach (var note in quickNotes)
            {
                var noteButton = new Button
                {
                    Text = note,
                    Location = new Point(x, y),
                    Size = new Size(80, 30),
                    BackColor = Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 8F),
                    Cursor = Cursors.Hand
                };
                noteButton.Click += (s, args) =>
                {
                    if (!notesTextBox.Text.Contains(note))
                    {
                        notesTextBox.Text += (string.IsNullOrEmpty(notesTextBox.Text) ? "" : "، ") + note;
                    }
                };
                notesForm.Controls.Add(noteButton);

                x += 85;
                if (x > 300)
                {
                    x = 20;
                    y += 35;
                }
            }

            var saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(200, 240),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += (s, args) =>
            {
                orderItem.Notes = notesTextBox.Text;
                UpdateOrderDisplay();
                notesForm.Close();
            };
            notesForm.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(290, 240),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, args) => notesForm.Close();
            notesForm.Controls.Add(cancelButton);

            notesForm.ShowDialog();
        }

        private void ShowTablesPanel()
        {
            var tablesForm = new Form
            {
                Text = "اختيار الطاولة",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var titleLabel = new Label
            {
                Text = "اختر الطاولة",
                Location = new Point(20, 20),
                Size = new Size(550, 30),
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter
            };
            tablesForm.Controls.Add(titleLabel);

            // Legend
            var legendPanel = new Panel
            {
                Location = new Point(20, 55),
                Size = new Size(550, 30),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var freeLabel = new Label
            {
                Text = "🔵 فارغة",
                Location = new Point(10, 5),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9F)
            };
            legendPanel.Controls.Add(freeLabel);

            var occupiedLabel = new Label
            {
                Text = "🔴 محجوزة",
                Location = new Point(100, 5),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9F)
            };
            legendPanel.Controls.Add(occupiedLabel);

            var activeLabel = new Label
            {
                Text = "🟢 نشطة",
                Location = new Point(190, 5),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9F)
            };
            legendPanel.Controls.Add(activeLabel);

            var infoLabel = new Label
            {
                Text = "اضغط على الطاولة المحجوزة لاسترجاع الفاتورة",
                Location = new Point(280, 5),
                Size = new Size(260, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Italic),
                ForeColor = Color.FromArgb(127, 140, 141)
            };
            legendPanel.Controls.Add(infoLabel);

            tablesForm.Controls.Add(legendPanel);

            // Create table buttons
            int x = 50, y = 100;
            for (int i = 1; i <= 20; i++)
            {
                bool isHeld = CheckTableHasHeldOrders(i);
                bool isCurrentTable = selectedTableNumber == i;

                var tableButton = new Button
                {
                    Text = $"طاولة {i}",
                    Location = new Point(x, y),
                    Size = new Size(80, 60),
                    BackColor = isHeld ? Color.FromArgb(231, 76, 60) :
                               isCurrentTable ? Color.FromArgb(46, 204, 113) :
                               Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                    Cursor = Cursors.Hand,
                    Tag = i
                };

                if (isHeld)
                {
                    try
                    {
                        var orderService = new OrderService();
                        var heldOrderItems = orderService.GetHeldOrdersForTable(i);
                        var itemCount = heldOrderItems.Sum(item => item.Quantity);
                        var totalAmount = heldOrderItems.Sum(item => item.Price * item.Quantity - item.DiscountAmount * item.Quantity);
                        tableButton.Text += $"\nمحجوزة\n{itemCount} صنف\n{totalAmount:F0} جنيه";
                    }
                    catch
                    {
                        tableButton.Text += "\nمحجوزة";
                    }
                }
                else if (isCurrentTable)
                {
                    tableButton.Text += "\nنشطة";
                }

                tableButton.Click += (s, args) =>
                {
                    int tableNum = (int)tableButton.Tag;

                    // Check if table has held orders in database first
                    bool hasHeldOrders = CheckTableHasHeldOrders(tableNum);

                    if (hasHeldOrders)
                    {
                        // Restore held order
                        RestoreHeldOrder(tableNum);
                        tablesForm.Close();
                    }
                    else
                    {
                        // Select new table
                        if (orderItems.Count > 0)
                        {
                            var result = MessageBox.Show(
                                "يوجد طلب حالي. هل تريد تعليقه قبل اختيار طاولة جديدة؟",
                                "تأكيد",
                                MessageBoxButtons.YesNoCancel,
                                MessageBoxIcon.Question);

                            if (result == DialogResult.Yes)
                            {
                                // Hold current order first
                                if (selectedTableNumber > 0)
                                {
                                    HoldCurrentOrder();
                                }
                            }
                            else if (result == DialogResult.Cancel)
                            {
                                return;
                            }
                            // If No, just switch tables without holding
                        }

                        selectedTableNumber = tableNum;
                        UpdateTableComboBox();
                        orderStartTime = DateTime.Now;
                        tablesForm.Close();
                    }
                };
                tablesForm.Controls.Add(tableButton);

                x += 90;
                if (x > 500)
                {
                    x = 50;
                    y += 70;
                }
            }

            tablesForm.ShowDialog();
        }

        private void ShowInvoicePanel()
        {
            if (orderItems.Count == 0)
            {
                MessageBox.Show("لا يوجد أصناف في الطلب", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoiceForm = new Form
            {
                Text = "فاتورة الطلب",
                Size = new Size(400, 600),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var invoiceText = GenerateInvoiceText();
            var invoiceLabel = new Label
            {
                Text = invoiceText,
                Location = new Point(20, 20),
                Size = new Size(350, 500),
                Font = new Font("Courier New", 10F),
                TextAlign = ContentAlignment.TopRight
            };
            invoiceForm.Controls.Add(invoiceLabel);

            var printButton = new Button
            {
                Text = "طباعة",
                Location = new Point(50, 530),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            printButton.Click += (s, args) =>
            {
                MessageBox.Show("تم إرسال الفاتورة للطباعة", "طباعة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            };
            invoiceForm.Controls.Add(printButton);

            var closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(200, 530),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeButton.Click += (s, args) => invoiceForm.Close();
            invoiceForm.Controls.Add(closeButton);

            invoiceForm.ShowDialog();
        }

        private string GenerateInvoiceText()
        {
            var invoice = new System.Text.StringBuilder();
            invoice.AppendLine("=============================");
            invoice.AppendLine("        فاتورة الطلب        ");
            invoice.AppendLine("=============================");
            invoice.AppendLine($"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm}");
            invoice.AppendLine($"الطاولة: {selectedTableNumber}");
            if (selectedOrderType == 2)
                invoice.AppendLine($"طيار الدليفري: {driversComboBox.Text}");
            else
                invoice.AppendLine($"مقدم الطلبات: {waiterComboBox.Text}");
            invoice.AppendLine("-----------------------------");

            foreach (var item in orderItems)
            {
                invoice.AppendLine($"{item.MenuItemName}");

                if (item.DiscountAmount > 0)
                {
                    invoice.AppendLine($"  السعر: {item.OriginalPrice:F2} جنيه");
                    invoice.AppendLine($"  خصم: {item.DiscountAmount:F2} جنيه");
                    invoice.AppendLine($"  {item.Quantity} × {(item.OriginalPrice - item.DiscountAmount):F2} = {item.Subtotal:F2}");
                }
                else
                {
                    invoice.AppendLine($"  {item.Quantity} × {item.Price:F2} = {(item.Quantity * item.Price):F2}");
                }

                if (!string.IsNullOrEmpty(item.Notes))
                {
                    invoice.AppendLine($"  ملاحظات: {item.Notes}");
                }
                invoice.AppendLine();
            }

            invoice.AppendLine("-----------------------------");

            // Calculate totals for invoice
            var originalTotal = orderItems.Sum(item => item.OriginalPrice > 0 ? item.OriginalPrice * item.Quantity : item.Price * item.Quantity);
            var itemsDiscount = orderItems.Sum(item => item.DiscountAmount * item.Quantity);
            var subtotalAfterItemDiscounts = originalTotal - itemsDiscount;

            if (itemsDiscount > 0)
            {
                invoice.AppendLine($"المجموع الأصلي: {originalTotal:F2} جنيه");
                invoice.AppendLine($"خصم الأصناف: {itemsDiscount:F2} جنيه");
                invoice.AppendLine($"المجموع الفرعي: {subtotalAfterItemDiscounts:F2} جنيه");
            }
            else
            {
                invoice.AppendLine($"المجموع الفرعي: {subtotalAfterItemDiscounts:F2} جنيه");
            }

            if (totalDiscount > 0)
            {
                invoice.AppendLine($"خصم إضافي: {totalDiscount:F2} جنيه");
            }

            invoice.AppendLine($"الإجمالي النهائي: {totalAmount:F2} جنيه");
            invoice.AppendLine("=============================");

            return invoice.ToString();
        }



        private void RestoreHeldOrder(int tableNumber)
        {
            try
            {
                // Try to load from database first
                var orderService = new OrderService();
                var heldOrderItems = orderService.GetHeldOrdersForTable(tableNumber);

                if (heldOrderItems.Any())
                {
                    // Restore from database
                    selectedTableNumber = tableNumber;
                    orderItems.Clear();
                    orderItems.AddRange(heldOrderItems);
                    orderStartTime = DateTime.Now;

                    // Update UI
                    UpdateTableComboBox();
                    UpdateOrderDisplay();

                    MessageBox.Show($"تم استرجاع فاتورة الطاولة {tableNumber}", "استرجاع الفاتورة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استرجاع الطلب من قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            // Fallback to memory if database fails
            if (!heldOrders.ContainsKey(tableNumber)) return;

            var heldOrder = heldOrders[tableNumber];

            // Restore order data
            selectedTableNumber = tableNumber;
            orderItems.Clear();
            orderItems.AddRange(heldOrder.OrderItems);
            customerNameTextBox.Text = heldOrder.CustomerName;
            customerPhoneTextBox.Text = heldOrder.CustomerPhone;
            // Set waiter name if exists in combo box
            var waiterIndex = waiterComboBox.Items.IndexOf(heldOrder.WaiterName);
            waiterComboBox.SelectedIndex = waiterIndex >= 0 ? waiterIndex : 0;
            SelectOrderType(heldOrder.OrderType);
            orderStartTime = heldOrder.StartTime;
            totalAmount = heldOrder.TotalAmount;

            // Update UI
            UpdateTableComboBox();
            UpdateOrderDisplay();

            // Remove from held orders
            heldOrders.Remove(tableNumber);

            MessageBox.Show($"تم استرجاع فاتورة الطاولة {tableNumber}", "استرجاع الفاتورة",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private bool CheckTableHasHeldOrders(int tableNumber)
        {
            try
            {
                var orderService = new OrderService();
                var heldOrders = orderService.GetHeldOrdersForTable(tableNumber);
                return heldOrders.Any();
            }
            catch
            {
                // If can't check database, fallback to memory
                return heldOrders.ContainsKey(tableNumber);
            }
        }

        private void HoldCurrentOrder()
        {
            if (orderItems.Count == 0 || selectedTableNumber == 0) return;

            var heldOrder = new HeldOrder
            {
                TableNumber = selectedTableNumber,
                OrderItems = new List<OrderItem>(orderItems),
                CustomerName = customerNameTextBox.Text,
                CustomerPhone = customerPhoneTextBox.Text,
                WaiterName = waiterComboBox.Text,
                OrderType = selectedOrderType,
                StartTime = orderStartTime,
                TotalAmount = totalAmount
            };

            heldOrders[selectedTableNumber] = heldOrder;

            // Clear current order
            orderItems.Clear();
            UpdateOrderDisplay();
        }

        private void UpdateTableComboBox()
        {
            // Update table combo box
            tableComboBox.Items.Clear();
            for (int i = 1; i <= 20; i++)
            {
                tableComboBox.Items.Add($"طاولة {i}");
            }

            if (selectedTableNumber > 0)
            {
                tableComboBox.SelectedIndex = selectedTableNumber - 1;
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            UpdateTableComboBox();
        }

        private void CustomerPhoneTextBox_TextChanged(object? sender, EventArgs e)
        {
            // Only search when delivery is selected and phone has enough digits
            if (selectedOrderType != 2 || customerPhoneTextBox.Text.Length < 8)
                return;

            string phone = customerPhoneTextBox.Text.Trim();
            if (string.IsNullOrEmpty(phone))
                return;

            // Search for customer by phone
            var customer = SearchCustomerByPhone(phone);
            if (customer != null)
            {
                // Customer found, fill the details
                customerNameTextBox.Text = customer.Name;
                customerAddressTextBox.Text = customer.Address;
                customerNameTextBox.BackColor = Color.LightGreen;
                customerAddressTextBox.BackColor = Color.LightGreen;
            }
            else
            {
                // Customer not found, clear fields and indicate new customer
                customerNameTextBox.BackColor = Color.LightYellow;
                customerAddressTextBox.BackColor = Color.LightYellow;

                // If phone is complete (11 digits), offer to create new customer
                if (phone.Length >= 11)
                {
                    var result = MessageBox.Show(
                        $"العميل برقم {phone} غير موجود.\nهل تريد إضافة عميل جديد؟",
                        "عميل جديد",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        ShowNewCustomerForm(phone);
                    }
                }
            }
        }

        private Customer? SearchCustomerByPhone(string phone)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT Id, Name, Phone, Email, Address FROM Customers WHERE Phone = @Phone";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Phone", phone);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return new Customer
                    {
                        Id = (int)reader["Id"],
                        Name = reader["Name"].ToString() ?? "",
                        Phone = reader["Phone"].ToString() ?? "",
                        Email = reader["Email"].ToString() ?? "",
                        Address = reader["Address"].ToString() ?? ""
                    };
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return null;
        }

        private void ShowNewCustomerForm(string phone)
        {
            var customerForm = new Form
            {
                Text = "إضافة عميل جديد",
                Size = new Size(400, 350),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes
            };

            var nameLabel = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(20, 30),
                Size = new Size(80, 25)
            };
            customerForm.Controls.Add(nameLabel);

            var nameTextBox = new TextBox
            {
                Location = new Point(110, 30),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10F)
            };
            customerForm.Controls.Add(nameTextBox);

            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(20, 70),
                Size = new Size(80, 25)
            };
            customerForm.Controls.Add(phoneLabel);

            var phoneTextBox = new TextBox
            {
                Text = phone,
                Location = new Point(110, 70),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10F),
                ReadOnly = true
            };
            customerForm.Controls.Add(phoneTextBox);

            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(20, 110),
                Size = new Size(80, 25)
            };
            customerForm.Controls.Add(emailLabel);

            var emailTextBox = new TextBox
            {
                Location = new Point(110, 110),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10F)
            };
            customerForm.Controls.Add(emailTextBox);

            var addressLabel = new Label
            {
                Text = "العنوان:",
                Location = new Point(20, 150),
                Size = new Size(80, 25)
            };
            customerForm.Controls.Add(addressLabel);

            var addressTextBox = new TextBox
            {
                Location = new Point(110, 150),
                Size = new Size(250, 60),
                Font = new Font("Segoe UI", 10F),
                Multiline = true
            };
            customerForm.Controls.Add(addressTextBox);

            var saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(200, 230),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White
            };
            saveButton.Click += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(nameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(addressTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال عنوان العميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Save new customer
                var newCustomer = new Customer
                {
                    Name = nameTextBox.Text.Trim(),
                    Phone = phone,
                    Email = emailTextBox.Text.Trim(),
                    Address = addressTextBox.Text.Trim(),
                    RegistrationDate = DateTime.Now
                };

                if (SaveNewCustomer(newCustomer))
                {
                    // Fill the order form with new customer data
                    customerNameTextBox.Text = newCustomer.Name;
                    customerAddressTextBox.Text = newCustomer.Address;
                    customerNameTextBox.BackColor = Color.LightGreen;
                    customerAddressTextBox.BackColor = Color.LightGreen;

                    MessageBox.Show("تم حفظ العميل الجديد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    customerForm.Close();
                }
            };
            customerForm.Controls.Add(saveButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(290, 230),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            cancelButton.Click += (s, e) => customerForm.Close();
            customerForm.Controls.Add(cancelButton);

            customerForm.ShowDialog();
        }

        private bool SaveNewCustomer(Customer customer)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"INSERT INTO Customers (Name, Phone, Email, Address, RegistrationDate)
                                VALUES (@Name, @Phone, @Email, @Address, @RegistrationDate)";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Name", customer.Name);
                command.Parameters.AddWithValue("@Phone", customer.Phone);
                command.Parameters.AddWithValue("@Email", customer.Email ?? "");
                command.Parameters.AddWithValue("@Address", customer.Address);
                command.Parameters.AddWithValue("@RegistrationDate", customer.RegistrationDate);

                command.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}
