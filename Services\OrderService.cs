using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Services
{
    public class OrderService
    {
        public List<Order> GetAllOrders()
        {
            var orders = new List<Order>();
            
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                
                string query = "SELECT TOP 10 Id, BranchId, TotalAmount, OrderDate FROM Orders ORDER BY OrderDate DESC";
                
                using var command = new SqlCommand(query, connection);
                using var reader = command.ExecuteReader();
                
                while (reader.Read())
                {
                    var order = new Order
                    {
                        Id = (int)reader["Id"],
                        BranchId = (int)reader["BranchId"],
                        TotalAmount = (decimal)reader["TotalAmount"],
                        OrderDate = (DateTime)reader["OrderDate"]
                    };
                    
                    orders.Add(order);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetAllOrders: {ex.Message}");
            }
            
            return orders;
        }

        public List<Order> GetOrdersByBranch(int branchId)
        {
            var orders = new List<Order>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"SELECT Id, BranchId, TableNumber, CustomerId, CustomerName, CustomerPhone,
                               CustomerEmail, CustomerAddress, Type, Status, OrderDate, SubTotal, Tax,
                               ServiceCharge, DeliveryFee, Discount, TotalAmount, Notes, EmployeeId,
                               PaymentMethod, IsPaid, DeliveryTime, DeliveryDriverName
                               FROM Orders WHERE BranchId = @BranchId ORDER BY OrderDate DESC";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@BranchId", branchId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    orders.Add(MapReaderToOrder(reader));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetOrdersByBranch: {ex.Message}");
            }

            return orders;
        }

        public Order? GetOrderById(int id)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"SELECT Id, BranchId, TableNumber, CustomerId, CustomerName, CustomerPhone,
                               CustomerEmail, CustomerAddress, Type, Status, OrderDate, SubTotal, Tax,
                               ServiceCharge, DeliveryFee, Discount, TotalAmount, Notes, EmployeeId,
                               PaymentMethod, IsPaid, DeliveryTime, DeliveryDriverName
                               FROM Orders WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapReaderToOrder(reader);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetOrderById: {ex.Message}");
            }

            return null;
        }

        public int CreateOrder(Order order, List<OrderItem> orderItems)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Insert order
                    string orderQuery = @"INSERT INTO Orders
                        (BranchId, TableNumber, CustomerId, CustomerName, CustomerPhone, CustomerEmail,
                         CustomerAddress, Type, Status, OrderDate, SubTotal, Tax, ServiceCharge,
                         DeliveryFee, Discount, TotalAmount, Notes, EmployeeId, PaymentMethod, IsPaid)
                        VALUES
                        (@BranchId, @TableNumber, @CustomerId, @CustomerName, @CustomerPhone, @CustomerEmail,
                         @CustomerAddress, @Type, @Status, @OrderDate, @SubTotal, @Tax, @ServiceCharge,
                         @DeliveryFee, @Discount, @TotalAmount, @Notes, @EmployeeId, @PaymentMethod, @IsPaid);
                        SELECT SCOPE_IDENTITY();";

                    using var orderCommand = new SqlCommand(orderQuery, connection, transaction);
                    AddOrderParameters(orderCommand, order);

                    int orderId = Convert.ToInt32(orderCommand.ExecuteScalar());

                    // Insert order items
                    foreach (var item in orderItems)
                    {
                        string itemQuery = @"INSERT INTO OrderItems
                            (OrderId, MenuItemId, MenuItemName, Price, Quantity, Notes)
                            VALUES (@OrderId, @MenuItemId, @MenuItemName, @Price, @Quantity, @Notes)";

                        using var itemCommand = new SqlCommand(itemQuery, connection, transaction);
                        itemCommand.Parameters.AddWithValue("@OrderId", orderId);
                        itemCommand.Parameters.AddWithValue("@MenuItemId", item.MenuItemId);
                        itemCommand.Parameters.AddWithValue("@MenuItemName", item.MenuItemName);
                        itemCommand.Parameters.AddWithValue("@Price", item.Price);
                        itemCommand.Parameters.AddWithValue("@Quantity", item.Quantity);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? "");
                        itemCommand.ExecuteNonQuery();
                    }

                    transaction.Commit();
                    return orderId;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في CreateOrder: {ex.Message}");
                throw;
            }
        }

        public void UpdateOrderStatus(int orderId, int status)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "UPDATE Orders SET Status = @Status WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Status", status);
                command.Parameters.AddWithValue("@Id", orderId);
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateOrderStatus: {ex.Message}");
                throw;
            }
        }

        public decimal GetTotalSales(DateTime fromDate, DateTime toDate)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"SELECT COALESCE(SUM(TotalAmount), 0) FROM Orders
                               WHERE OrderDate BETWEEN @FromDate AND @ToDate";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate.AddDays(1).AddSeconds(-1));

                var result = command.ExecuteScalar();
                return result == DBNull.Value ? 0 : Convert.ToDecimal(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetTotalSales: {ex.Message}");
                return 0;
            }
        }

        private Order MapReaderToOrder(SqlDataReader reader)
        {
            return new Order
            {
                Id = reader.GetInt32(0), // Id
                BranchId = reader.GetInt32(1), // BranchId
                TableNumber = reader.IsDBNull(2) ? null : reader.GetInt32(2), // TableNumber
                CustomerName = reader.IsDBNull(4) ? "" : reader.GetString(4), // CustomerName
                CustomerPhone = reader.IsDBNull(5) ? "" : reader.GetString(5), // CustomerPhone
                CustomerEmail = reader.IsDBNull(6) ? "" : reader.GetString(6), // CustomerEmail
                CustomerAddress = reader.IsDBNull(7) ? "" : reader.GetString(7), // CustomerAddress
                Type = (OrderType)reader.GetInt32(8), // Type
                Status = (OrderStatus)reader.GetInt32(9), // Status
                OrderDate = reader.GetDateTime(10), // OrderDate
                SubTotal = reader.GetDecimal(11), // SubTotal
                Tax = reader.GetDecimal(12), // Tax
                ServiceCharge = reader.GetDecimal(13), // ServiceCharge
                DeliveryFee = reader.GetDecimal(14), // DeliveryFee
                Discount = reader.GetDecimal(15), // Discount
                TotalAmount = reader.GetDecimal(16), // TotalAmount
                Notes = reader.IsDBNull(17) ? "" : reader.GetString(17), // Notes
                EmployeeId = reader.GetInt32(18), // EmployeeId
                PaymentMethod = reader.IsDBNull(19) ? "نقدي" : reader.GetString(19), // PaymentMethod
                IsPaid = reader.GetBoolean(20), // IsPaid
                DeliveryTime = reader.IsDBNull(21) ? null : reader.GetDateTime(21), // DeliveryTime
                DeliveryDriverName = reader.IsDBNull(22) ? "" : reader.GetString(22) // DeliveryDriverName
            };
        }

        private void AddOrderParameters(SqlCommand command, Order order)
        {
            command.Parameters.AddWithValue("@BranchId", order.BranchId);
            command.Parameters.AddWithValue("@TableNumber", (object?)order.TableNumber ?? DBNull.Value);
            command.Parameters.AddWithValue("@CustomerId", DBNull.Value);
            command.Parameters.AddWithValue("@CustomerName", order.CustomerName ?? "");
            command.Parameters.AddWithValue("@CustomerPhone", order.CustomerPhone ?? "");
            command.Parameters.AddWithValue("@CustomerEmail", order.CustomerEmail ?? "");
            command.Parameters.AddWithValue("@CustomerAddress", order.CustomerAddress ?? "");
            command.Parameters.AddWithValue("@Type", (int)order.Type);
            command.Parameters.AddWithValue("@Status", (int)order.Status);
            command.Parameters.AddWithValue("@OrderDate", order.OrderDate);
            command.Parameters.AddWithValue("@SubTotal", order.SubTotal);
            command.Parameters.AddWithValue("@Tax", order.Tax);
            command.Parameters.AddWithValue("@ServiceCharge", order.ServiceCharge);
            command.Parameters.AddWithValue("@DeliveryFee", order.DeliveryFee);
            command.Parameters.AddWithValue("@Discount", order.Discount);
            command.Parameters.AddWithValue("@TotalAmount", order.TotalAmount);
            command.Parameters.AddWithValue("@Notes", order.Notes ?? "");
            command.Parameters.AddWithValue("@EmployeeId", order.EmployeeId);
            command.Parameters.AddWithValue("@PaymentMethod", order.PaymentMethod ?? "نقدي");
            command.Parameters.AddWithValue("@IsPaid", order.IsPaid);
            command.Parameters.AddWithValue("@DeliveryTime", (object?)order.DeliveryTime ?? DBNull.Value);
            command.Parameters.AddWithValue("@DeliveryDriverName", order.DeliveryDriverName ?? "");
        }

        public List<OrderItem> GetHeldOrdersForTable(int tableNumber)
        {
            var items = new List<OrderItem>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"
                    SELECT ItemName, Price, Quantity, OriginalPrice, DiscountAmount
                    FROM HeldOrders
                    WHERE TableNumber = @TableNumber";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@TableNumber", tableNumber);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var item = new OrderItem
                    {
                        MenuItemId = GetMenuItemIdByName(reader["ItemName"].ToString() ?? ""),
                        MenuItemName = reader["ItemName"].ToString() ?? "",
                        Price = Convert.ToDecimal(reader["Price"]),
                        Quantity = Convert.ToInt32(reader["Quantity"]),
                        OriginalPrice = reader["OriginalPrice"] != DBNull.Value ? Convert.ToDecimal(reader["OriginalPrice"]) : 0,
                        DiscountAmount = reader["DiscountAmount"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountAmount"]) : 0
                    };
                    items.Add(item);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع الطلبات المعلقة للطاولة {tableNumber}: {ex.Message}");
            }

            return items;
        }

        public void SaveHeldOrder(int tableNumber, List<OrderItem> items)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // Clear existing held orders for this table
                    string deleteQuery = "DELETE FROM HeldOrders WHERE TableNumber = @TableNumber";
                    using var deleteCommand = new SqlCommand(deleteQuery, connection, transaction);
                    deleteCommand.Parameters.AddWithValue("@TableNumber", tableNumber);
                    deleteCommand.ExecuteNonQuery();

                    // Insert new held orders
                    string insertQuery = @"
                        INSERT INTO HeldOrders (TableNumber, ItemName, Price, Quantity, OriginalPrice, DiscountAmount, HeldAt)
                        VALUES (@TableNumber, @ItemName, @Price, @Quantity, @OriginalPrice, @DiscountAmount, @HeldAt)";

                    foreach (var item in items)
                    {
                        using var insertCommand = new SqlCommand(insertQuery, connection, transaction);
                        insertCommand.Parameters.AddWithValue("@TableNumber", tableNumber);
                        insertCommand.Parameters.AddWithValue("@ItemName", item.MenuItemName);
                        insertCommand.Parameters.AddWithValue("@Price", item.Price);
                        insertCommand.Parameters.AddWithValue("@Quantity", item.Quantity);
                        insertCommand.Parameters.AddWithValue("@OriginalPrice", item.OriginalPrice);
                        insertCommand.Parameters.AddWithValue("@DiscountAmount", item.DiscountAmount);
                        insertCommand.Parameters.AddWithValue("@HeldAt", DateTime.Now);
                        insertCommand.ExecuteNonQuery();
                    }

                    transaction.Commit();
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الطلبات المعلقة للطاولة {tableNumber}: {ex.Message}");
            }
        }

        public void ClearHeldOrder(int tableNumber)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "DELETE FROM HeldOrders WHERE TableNumber = @TableNumber";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@TableNumber", tableNumber);
                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في مسح الطلبات المعلقة للطاولة {tableNumber}: {ex.Message}");
            }
        }

        public void UpdateTableStatus(int tableNumber, TableStatus status)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // Check if table exists, if not create it
                string checkQuery = "SELECT COUNT(*) FROM Tables WHERE Number = @TableNumber";
                using var checkCommand = new SqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@TableNumber", tableNumber);

                int tableExists = (int)checkCommand.ExecuteScalar();

                if (tableExists == 0)
                {
                    // Create table if it doesn't exist
                    string insertQuery = @"
                        INSERT INTO Tables (Number, Capacity, Status, BranchId)
                        VALUES (@TableNumber, 4, @Status, 1)";
                    using var insertCommand = new SqlCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@TableNumber", tableNumber);
                    insertCommand.Parameters.AddWithValue("@Status", (int)status);
                    insertCommand.ExecuteNonQuery();
                }
                else
                {
                    // Update existing table status
                    string updateQuery = "UPDATE Tables SET Status = @Status WHERE Number = @TableNumber";
                    using var updateCommand = new SqlCommand(updateQuery, connection);
                    updateCommand.Parameters.AddWithValue("@TableNumber", tableNumber);
                    updateCommand.Parameters.AddWithValue("@Status", (int)status);
                    updateCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث حالة الطاولة {tableNumber}: {ex.Message}");
            }
        }

        private int GetMenuItemIdByName(string itemName)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT Id FROM MenuItems WHERE Name = @Name";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Name", itemName);

                var result = command.ExecuteScalar();
                return result != null ? (int)result : 1; // Default to 1 if not found
            }
            catch
            {
                return 1; // Default MenuItemId if lookup fails
            }
        }
    }
}