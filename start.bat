@echo off
chcp 65001 >nul
cls
echo ========================================
echo        Restaurant Management System
echo ========================================
echo.
echo Choose an option:
echo.
echo 1. Setup Database (First time only)
echo 2. Build Application
echo 3. Run Application
echo 4. Build and Run
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto build
if "%choice%"=="3" goto run
if "%choice%"=="4" goto buildrun
if "%choice%"=="5" goto exit
goto invalid

:setup
cls
echo ========================================
echo           Database Setup
echo ========================================
echo.
echo Please follow these steps:
echo.
echo 1. Open SQL Server Management Studio
echo 2. Connect to your SQL Server instance
echo 3. Open and execute: setup_database.sql
echo 4. Make sure the database 'RestaurantDB' is created
echo.
echo Press any key when done...
pause >nul
goto menu

:build
cls
echo ========================================
echo           Building Application
echo ========================================
echo.
dotnet build RestaurantManagement.csproj --configuration Release
echo.
if %ERRORLEVEL% EQU 0 (
    echo Build completed successfully!
) else (
    echo Build failed! Check errors above.
)
echo.
pause
goto menu

:run
cls
echo ========================================
echo           Running Application
echo ========================================
echo.
dotnet run --project RestaurantManagement.csproj
goto menu

:buildrun
cls
echo ========================================
echo         Build and Run Application
echo ========================================
echo.
echo Building...
dotnet build RestaurantManagement.csproj --configuration Release
echo.
if %ERRORLEVEL% EQU 0 (
    echo Build successful! Starting application...
    echo.
    dotnet run --project RestaurantManagement.csproj
) else (
    echo Build failed! Cannot start application.
    pause
)
goto menu

:invalid
cls
echo Invalid choice! Please try again.
timeout /t 2 >nul
goto menu

:menu
cls
echo ========================================
echo        Restaurant Management System
echo ========================================
echo.
echo Choose an option:
echo.
echo 1. Setup Database (First time only)
echo 2. Build Application
echo 3. Run Application
echo 4. Build and Run
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto build
if "%choice%"=="3" goto run
if "%choice%"=="4" goto buildrun
if "%choice%"=="5" goto exit
goto invalid

:exit
echo.
echo Thank you for using Restaurant Management System!
echo.
timeout /t 2 >nul
exit