using System;

namespace RestaurantManagement.Models
{
    public enum TableStatus
    {
        Available,  // متاح
        Occupied,   // مشغول
        Reserved,   // محجوز
        Cleaning    // قيد التنظيف
    }

    public class Table
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public int Number { get; set; }
        public int Capacity { get; set; }
        public TableStatus Status { get; set; } = TableStatus.Available;
        public string Location { get; set; } = string.Empty;
        public DateTime? ReservationTime { get; set; }
        public string ReservedBy { get; set; } = string.Empty;
        public string ReservationPhone { get; set; } = string.Empty;
        public string QRCode { get; set; } = string.Empty; // للطلب عبر QR Code
    }

    public class Reservation
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public int TableId { get; set; }
        public int TableNumber { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public DateTime ReservationDate { get; set; }
        public DateTime ReservationTime { get; set; }
        public int PartySize { get; set; }
        public string Notes { get; set; } = string.Empty;
        public bool IsConfirmed { get; set; } = false;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CustomerEmail { get; set; } = string.Empty;
        public bool SendReminder { get; set; } = true;
    }
}