using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Services
{
    public class EmployeeService
    {
        public List<Employee> GetAllEmployees(int branchId = 0)
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0 
                    ? "SELECT e.Id, e.BranchId, b.Name as BranchName, e.Name, e.Phone, e.Email, e.Role, e.Salary, e.HireDate, e.IsActive, e.Username, e.Password, e.CanAccessAllBranches FROM Employees e INNER JOIN Branches b ON e.BranchId = b.Id WHERE e.BranchId = @BranchId ORDER BY e.Name"
                    : "SELECT e.Id, e.BranchId, b.Name as BranchName, e.Name, e.Phone, e.Email, e.<PERSON>, e.<PERSON>, e.HireDate, e.<PERSON>, e.Username, e.Password, e.CanAccessAllBranches FROM Employees e INNER JOIN Branches b ON e.BranchId = b.Id ORDER BY b.Name, e.Name";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Phone = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Email = reader.IsDBNull(5) ? "" : reader.GetString(5),
                        Role = (EmployeeRole)reader.GetInt32(6),
                        Salary = reader.GetDecimal(7),
                        HireDate = reader.GetDateTime(8),
                        IsActive = reader.GetBoolean(9),
                        Username = reader.GetString(10),
                        Password = reader.GetString(11),
                        CanAccessAllBranches = reader.GetBoolean(12)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات الموظفين: {ex.Message}");
            }

            return employees;
        }

        public List<Employee> GetActiveEmployees(int branchId = 0)
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0 
                    ? "SELECT e.Id, e.BranchId, b.Name as BranchName, e.Name, e.Phone, e.Email, e.Role, e.Salary, e.HireDate, e.IsActive, e.Username, e.Password, e.CanAccessAllBranches FROM Employees e INNER JOIN Branches b ON e.BranchId = b.Id WHERE e.BranchId = @BranchId AND e.IsActive = 1 ORDER BY e.Name"
                    : "SELECT e.Id, e.BranchId, b.Name as BranchName, e.Name, e.Phone, e.Email, e.Role, e.Salary, e.HireDate, e.IsActive, e.Username, e.Password, e.CanAccessAllBranches FROM Employees e INNER JOIN Branches b ON e.BranchId = b.Id WHERE e.IsActive = 1 ORDER BY b.Name, e.Name";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Phone = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Email = reader.IsDBNull(5) ? "" : reader.GetString(5),
                        Role = (EmployeeRole)reader.GetInt32(6),
                        Salary = reader.GetDecimal(7),
                        HireDate = reader.GetDateTime(8),
                        IsActive = reader.GetBoolean(9),
                        Username = reader.GetString(10),
                        Password = reader.GetString(11),
                        CanAccessAllBranches = reader.GetBoolean(12)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات الموظفين النشطين: {ex.Message}");
            }

            return employees;
        }

        public bool AddEmployee(Employee employee)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"INSERT INTO Employees 
                    (BranchId, Name, Phone, Email, Role, Salary, HireDate, IsActive, Username, Password, CanAccessAllBranches)
                    VALUES 
                    (@BranchId, @Name, @Phone, @Email, @Role, @Salary, @HireDate, @IsActive, @Username, @Password, @CanAccessAllBranches)";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@BranchId", employee.BranchId);
                command.Parameters.AddWithValue("@Name", employee.Name);
                command.Parameters.AddWithValue("@Phone", employee.Phone ?? "");
                command.Parameters.AddWithValue("@Email", employee.Email ?? "");
                command.Parameters.AddWithValue("@Role", (int)employee.Role);
                command.Parameters.AddWithValue("@Salary", employee.Salary);
                command.Parameters.AddWithValue("@HireDate", employee.HireDate);
                command.Parameters.AddWithValue("@IsActive", employee.IsActive);
                command.Parameters.AddWithValue("@Username", employee.Username);
                command.Parameters.AddWithValue("@Password", employee.Password);
                command.Parameters.AddWithValue("@CanAccessAllBranches", employee.CanAccessAllBranches);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الموظف: {ex.Message}");
            }
        }

        public bool UpdateEmployee(Employee employee)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"UPDATE Employees SET 
                    Name = @Name, Phone = @Phone, Email = @Email, Role = @Role, 
                    Salary = @Salary, HireDate = @HireDate, IsActive = @IsActive, 
                    Username = @Username, Password = @Password, CanAccessAllBranches = @CanAccessAllBranches
                    WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", employee.Id);
                command.Parameters.AddWithValue("@Name", employee.Name);
                command.Parameters.AddWithValue("@Phone", employee.Phone ?? "");
                command.Parameters.AddWithValue("@Email", employee.Email ?? "");
                command.Parameters.AddWithValue("@Role", (int)employee.Role);
                command.Parameters.AddWithValue("@Salary", employee.Salary);
                command.Parameters.AddWithValue("@HireDate", employee.HireDate);
                command.Parameters.AddWithValue("@IsActive", employee.IsActive);
                command.Parameters.AddWithValue("@Username", employee.Username);
                command.Parameters.AddWithValue("@Password", employee.Password);
                command.Parameters.AddWithValue("@CanAccessAllBranches", employee.CanAccessAllBranches);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث بيانات الموظف: {ex.Message}");
            }
        }

        public bool DeleteEmployee(int employeeId)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                // بدلاً من الحذف، نقوم بتعطيل الموظف
                string query = "UPDATE Employees SET IsActive = 0 WHERE Id = @Id";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", employeeId);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الموظف: {ex.Message}");
            }
        }

        public bool IsUsernameExists(string username, int excludeEmployeeId = 0)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = excludeEmployeeId > 0 
                    ? "SELECT COUNT(*) FROM Employees WHERE Username = @Username AND Id != @ExcludeId"
                    : "SELECT COUNT(*) FROM Employees WHERE Username = @Username";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Username", username);
                if (excludeEmployeeId > 0)
                    command.Parameters.AddWithValue("@ExcludeId", excludeEmployeeId);

                return (int)command.ExecuteScalar() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من اسم المستخدم: {ex.Message}");
            }
        }

        public Employee? GetEmployeeByUsername(string username)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT e.Id, e.BranchId, b.Name as BranchName, e.Name, e.Phone, e.Email, e.Role, e.Salary, e.HireDate, e.IsActive, e.Username, e.Password, e.CanAccessAllBranches FROM Employees e INNER JOIN Branches b ON e.BranchId = b.Id WHERE e.Username = @Username AND e.IsActive = 1";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Username", username);

                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new Employee
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Phone = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Email = reader.IsDBNull(5) ? "" : reader.GetString(5),
                        Role = (EmployeeRole)reader.GetInt32(6),
                        Salary = reader.GetDecimal(7),
                        HireDate = reader.GetDateTime(8),
                        IsActive = reader.GetBoolean(9),
                        Username = reader.GetString(10),
                        Password = reader.GetString(11),
                        CanAccessAllBranches = reader.GetBoolean(12)
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات الموظف: {ex.Message}");
            }
        }
    }
}
