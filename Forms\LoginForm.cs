using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class LoginForm : Form
    {
        private BranchService branchService;
        public Employee? LoggedInEmployee { get; private set; }
        public Branch? SelectedBranch { get; private set; }

        public LoginForm()
        {
            InitializeComponent();
            branchService = new BranchService();
            LoadBranches();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // LoginForm
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(400, 350);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.Name = "LoginForm";
            this.Text = "تسجيل الدخول";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.White;
            
            CreateControls();
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private Label titleLabel;
        private Label branchLabel;
        private ComboBox branchComboBox;
        private Label usernameLabel;
        private TextBox usernameTextBox;
        private Label passwordLabel;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button cancelButton;
        private PictureBox logoBox;

        private void CreateControls()
        {
            // Logo/Title area
            logoBox = new PictureBox();
            logoBox.Size = new Size(64, 64);
            logoBox.Location = new Point(168, 20);
            logoBox.BackColor = Color.LightBlue;
            logoBox.BorderStyle = BorderStyle.FixedSingle;
            
            titleLabel = new Label();
            titleLabel.Text = "نظام إدارة المطعم";
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkBlue;
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.Size = new Size(300, 30);
            titleLabel.Location = new Point(50, 95);
            
            // Branch selection
            branchLabel = new Label();
            branchLabel.Text = "الفرع:";
            branchLabel.Size = new Size(80, 23);
            branchLabel.Location = new Point(300, 140);
            branchLabel.TextAlign = ContentAlignment.MiddleRight;
            
            branchComboBox = new ComboBox();
            branchComboBox.Size = new Size(200, 23);
            branchComboBox.Location = new Point(80, 140);
            branchComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            
            // Username
            usernameLabel = new Label();
            usernameLabel.Text = "اسم المستخدم:";
            usernameLabel.Size = new Size(80, 23);
            usernameLabel.Location = new Point(300, 180);
            usernameLabel.TextAlign = ContentAlignment.MiddleRight;
            
            usernameTextBox = new TextBox();
            usernameTextBox.Size = new Size(200, 23);
            usernameTextBox.Location = new Point(80, 180);
            usernameTextBox.Text = "admin"; // للاختبار
            
            // Password
            passwordLabel = new Label();
            passwordLabel.Text = "كلمة المرور:";
            passwordLabel.Size = new Size(80, 23);
            passwordLabel.Location = new Point(300, 220);
            passwordLabel.TextAlign = ContentAlignment.MiddleRight;
            
            passwordTextBox = new TextBox();
            passwordTextBox.Size = new Size(200, 23);
            passwordTextBox.Location = new Point(80, 220);
            passwordTextBox.UseSystemPasswordChar = true;
            passwordTextBox.Text = "admin123"; // للاختبار
            
            // Buttons
            loginButton = new Button();
            loginButton.Text = "دخول";
            loginButton.Size = new Size(80, 35);
            loginButton.Location = new Point(200, 270);
            loginButton.BackColor = Color.DodgerBlue;
            loginButton.ForeColor = Color.White;
            loginButton.FlatStyle = FlatStyle.Flat;
            loginButton.Click += LoginButton_Click;
            
            cancelButton = new Button();
            cancelButton.Text = "إلغاء";
            cancelButton.Size = new Size(80, 35);
            cancelButton.Location = new Point(100, 270);
            cancelButton.BackColor = Color.Gray;
            cancelButton.ForeColor = Color.White;
            cancelButton.FlatStyle = FlatStyle.Flat;
            cancelButton.Click += CancelButton_Click;
            
            // Add controls to form
            this.Controls.AddRange(new Control[] {
                logoBox, titleLabel, branchLabel, branchComboBox,
                usernameLabel, usernameTextBox, passwordLabel, passwordTextBox,
                loginButton, cancelButton
            });
            
            // Set tab order
            branchComboBox.TabIndex = 0;
            usernameTextBox.TabIndex = 1;
            passwordTextBox.TabIndex = 2;
            loginButton.TabIndex = 3;
            cancelButton.TabIndex = 4;
            
            // Set default button
            this.AcceptButton = loginButton;
            this.CancelButton = cancelButton;
        }

        private void LoadBranches()
        {
            try
            {
                var branches = branchService.GetAllBranches();
                branchComboBox.DataSource = branches;
                branchComboBox.DisplayMember = "Name";
                branchComboBox.ValueMember = "Id";
                
                if (branches.Count > 0)
                {
                    branchComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoginButton_Click(object? sender, EventArgs e)
        {
            if (ValidateInput())
            {
                if (AuthenticateUser())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (branchComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفرع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                branchComboBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(usernameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                usernameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(passwordTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                passwordTextBox.Focus();
                return false;
            }
            
            return true;
        }

        private bool AuthenticateUser()
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                
                string query = @"SELECT e.*, b.Name as BranchName FROM Employees e 
                               INNER JOIN Branches b ON e.BranchId = b.Id 
                               WHERE e.Username = @Username AND e.Password = @Password AND e.IsActive = 1 
                               AND (e.BranchId = @BranchId OR e.CanAccessAllBranches = 1)";
                
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Username", usernameTextBox.Text.Trim());
                command.Parameters.AddWithValue("@Password", passwordTextBox.Text); // في التطبيق الحقيقي يجب تشفير كلمة المرور
                command.Parameters.AddWithValue("@BranchId", branchComboBox.SelectedValue);
                
                using var reader = command.ExecuteReader();
                
                if (reader.Read())
                {
                    LoggedInEmployee = new Employee
                    {
                        Id = (int)reader["Id"],
                        BranchId = (int)reader["BranchId"],
                        BranchName = (string)reader["BranchName"],
                        Name = (string)reader["Name"],
                        Phone = reader["Phone"] == DBNull.Value ? "" : (string)reader["Phone"],
                        Email = reader["Email"] == DBNull.Value ? "" : (string)reader["Email"],
                        Role = (EmployeeRole)(int)reader["Role"],
                        Salary = (decimal)reader["Salary"],
                        HireDate = (DateTime)reader["HireDate"],
                        IsActive = (bool)reader["IsActive"],
                        Username = (string)reader["Username"],
                        CanAccessAllBranches = (bool)reader["CanAccessAllBranches"]
                    };
                    
                    SelectedBranch = (Branch)branchComboBox.SelectedItem;
                    
                    // تسجيل بداية المناوبة
                    RecordShiftStart();
                    
                    return true;
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة، أو ليس لديك صلاحية للوصول لهذا الفرع", 
                        "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private void RecordShiftStart()
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                
                // التحقق من وجود مناوبة مفتوحة
                string checkQuery = @"SELECT COUNT(*) FROM Shifts 
                                    WHERE EmployeeId = @EmployeeId AND EndTime IS NULL";
                
                using var checkCommand = new SqlCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@EmployeeId", LoggedInEmployee!.Id);
                
                int openShifts = (int)checkCommand.ExecuteScalar();
                
                if (openShifts == 0)
                {
                    // إنشاء مناوبة جديدة
                    string insertQuery = @"INSERT INTO Shifts (BranchId, EmployeeId, StartTime) 
                                         VALUES (@BranchId, @EmployeeId, GETDATE())";
                    
                    using var insertCommand = new SqlCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@BranchId", SelectedBranch!.Id);
                    insertCommand.Parameters.AddWithValue("@EmployeeId", LoggedInEmployee.Id);
                    insertCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                // لا نعرض رسالة خطأ للمستخدم، فقط نسجل في السجل
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل بداية المناوبة: {ex.Message}");
            }
        }
    }
}