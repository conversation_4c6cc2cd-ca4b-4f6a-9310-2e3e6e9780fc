using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Services
{
    public class InventoryService
    {
        public List<InventoryItem> GetAllInventoryItems(int branchId = 0)
        {
            var items = new List<InventoryItem>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0 
                    ? "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id WHERE i.BranchId = @BranchId ORDER BY i.Category, i.Name"
                    : "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id ORDER BY b.Name, i.Category, i.Name";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    items.Add(new InventoryItem
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Description = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Category = reader.GetString(5),
                        Unit = reader.GetString(6),
                        CurrentStock = reader.GetDecimal(7),
                        MinimumStock = reader.GetDecimal(8),
                        MaximumStock = reader.GetDecimal(9),
                        UnitCost = reader.GetDecimal(10),
                        Supplier = reader.IsDBNull(11) ? "" : reader.GetString(11),
                        SupplierPhone = reader.IsDBNull(12) ? "" : reader.GetString(12),
                        ExpiryDate = reader.GetDateTime(13),
                        LastUpdated = reader.GetDateTime(14),
                        Barcode = reader.IsDBNull(15) ? "" : reader.GetString(15)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات المخزون: {ex.Message}");
            }

            return items;
        }

        public List<InventoryItem> GetLowStockItems(int branchId = 0)
        {
            var items = new List<InventoryItem>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0 
                    ? "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id WHERE i.BranchId = @BranchId AND i.CurrentStock <= i.MinimumStock ORDER BY i.Category, i.Name"
                    : "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id WHERE i.CurrentStock <= i.MinimumStock ORDER BY b.Name, i.Category, i.Name";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    items.Add(new InventoryItem
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Description = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Category = reader.GetString(5),
                        Unit = reader.GetString(6),
                        CurrentStock = reader.GetDecimal(7),
                        MinimumStock = reader.GetDecimal(8),
                        MaximumStock = reader.GetDecimal(9),
                        UnitCost = reader.GetDecimal(10),
                        Supplier = reader.IsDBNull(11) ? "" : reader.GetString(11),
                        SupplierPhone = reader.IsDBNull(12) ? "" : reader.GetString(12),
                        ExpiryDate = reader.GetDateTime(13),
                        LastUpdated = reader.GetDateTime(14),
                        Barcode = reader.IsDBNull(15) ? "" : reader.GetString(15)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المواد منخفضة المخزون: {ex.Message}");
            }

            return items;
        }

        public List<InventoryItem> GetExpiringSoonItems(int branchId = 0, int daysAhead = 7)
        {
            var items = new List<InventoryItem>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0 
                    ? "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id WHERE i.BranchId = @BranchId AND i.ExpiryDate <= DATEADD(day, @DaysAhead, GETDATE()) ORDER BY i.ExpiryDate, i.Name"
                    : "SELECT i.Id, i.BranchId, b.Name as BranchName, i.Name, i.Description, i.Category, i.Unit, i.CurrentStock, i.MinimumStock, i.MaximumStock, i.UnitCost, i.Supplier, i.SupplierPhone, i.ExpiryDate, i.LastUpdated, i.Barcode FROM InventoryItems i INNER JOIN Branches b ON i.BranchId = b.Id WHERE i.ExpiryDate <= DATEADD(day, @DaysAhead, GETDATE()) ORDER BY i.ExpiryDate, b.Name, i.Name";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@DaysAhead", daysAhead);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    items.Add(new InventoryItem
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        BranchName = reader.GetString(2),
                        Name = reader.GetString(3),
                        Description = reader.IsDBNull(4) ? "" : reader.GetString(4),
                        Category = reader.GetString(5),
                        Unit = reader.GetString(6),
                        CurrentStock = reader.GetDecimal(7),
                        MinimumStock = reader.GetDecimal(8),
                        MaximumStock = reader.GetDecimal(9),
                        UnitCost = reader.GetDecimal(10),
                        Supplier = reader.IsDBNull(11) ? "" : reader.GetString(11),
                        SupplierPhone = reader.IsDBNull(12) ? "" : reader.GetString(12),
                        ExpiryDate = reader.GetDateTime(13),
                        LastUpdated = reader.GetDateTime(14),
                        Barcode = reader.IsDBNull(15) ? "" : reader.GetString(15)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المواد منتهية الصلاحية: {ex.Message}");
            }

            return items;
        }

        public bool AddInventoryItem(InventoryItem item)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"INSERT INTO InventoryItems
                    (BranchId, Name, Description, Category, Unit, CurrentStock, MinimumStock, MaximumStock, UnitCost, Supplier, SupplierPhone, ExpiryDate, LastUpdated, Barcode)
                    VALUES
                    (@BranchId, @Name, @Description, @Category, @Unit, @CurrentStock, @MinimumStock, @MaximumStock, @UnitCost, @Supplier, @SupplierPhone, @ExpiryDate, @LastUpdated, @Barcode)";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@BranchId", item.BranchId);
                command.Parameters.AddWithValue("@Name", item.Name);
                command.Parameters.AddWithValue("@Description", item.Description ?? "");
                command.Parameters.AddWithValue("@Category", item.Category);
                command.Parameters.AddWithValue("@Unit", item.Unit);
                command.Parameters.AddWithValue("@CurrentStock", item.CurrentStock);
                command.Parameters.AddWithValue("@MinimumStock", item.MinimumStock);
                command.Parameters.AddWithValue("@MaximumStock", item.MaximumStock);
                command.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                command.Parameters.AddWithValue("@Supplier", item.Supplier ?? "");
                command.Parameters.AddWithValue("@SupplierPhone", item.SupplierPhone ?? "");
                command.Parameters.AddWithValue("@ExpiryDate", item.ExpiryDate);
                command.Parameters.AddWithValue("@LastUpdated", DateTime.Now);
                command.Parameters.AddWithValue("@Barcode", item.Barcode ?? "");

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة صنف المخزون: {ex.Message}");
            }
        }

        public bool UpdateInventoryItem(InventoryItem item)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"UPDATE InventoryItems SET
                    Name = @Name, Description = @Description, Category = @Category, Unit = @Unit,
                    CurrentStock = @CurrentStock, MinimumStock = @MinimumStock, MaximumStock = @MaximumStock,
                    UnitCost = @UnitCost, Supplier = @Supplier, SupplierPhone = @SupplierPhone,
                    ExpiryDate = @ExpiryDate, LastUpdated = @LastUpdated, Barcode = @Barcode
                    WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", item.Id);
                command.Parameters.AddWithValue("@Name", item.Name);
                command.Parameters.AddWithValue("@Description", item.Description ?? "");
                command.Parameters.AddWithValue("@Category", item.Category);
                command.Parameters.AddWithValue("@Unit", item.Unit);
                command.Parameters.AddWithValue("@CurrentStock", item.CurrentStock);
                command.Parameters.AddWithValue("@MinimumStock", item.MinimumStock);
                command.Parameters.AddWithValue("@MaximumStock", item.MaximumStock);
                command.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                command.Parameters.AddWithValue("@Supplier", item.Supplier ?? "");
                command.Parameters.AddWithValue("@SupplierPhone", item.SupplierPhone ?? "");
                command.Parameters.AddWithValue("@ExpiryDate", item.ExpiryDate);
                command.Parameters.AddWithValue("@LastUpdated", DateTime.Now);
                command.Parameters.AddWithValue("@Barcode", item.Barcode ?? "");

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث صنف المخزون: {ex.Message}");
            }
        }

        public bool DeleteInventoryItem(int itemId)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "DELETE FROM InventoryItems WHERE Id = @Id";
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", itemId);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف صنف المخزون: {ex.Message}");
            }
        }

        public bool UpdateStock(int itemId, decimal newStock, string transactionType, int employeeId, string notes = "")
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();
                using var transaction = connection.BeginTransaction();

                try
                {
                    // تحديث المخزون
                    string updateQuery = "UPDATE InventoryItems SET CurrentStock = @NewStock, LastUpdated = @LastUpdated WHERE Id = @Id";
                    using var updateCommand = new SqlCommand(updateQuery, connection, transaction);
                    updateCommand.Parameters.AddWithValue("@Id", itemId);
                    updateCommand.Parameters.AddWithValue("@NewStock", newStock);
                    updateCommand.Parameters.AddWithValue("@LastUpdated", DateTime.Now);
                    updateCommand.ExecuteNonQuery();

                    // إضافة سجل الحركة
                    string insertQuery = @"INSERT INTO StockTransactions
                        (BranchId, InventoryItemId, ItemName, Quantity, TransactionType, UnitCost, Notes, TransactionDate, EmployeeId, EmployeeName)
                        SELECT i.BranchId, i.Id, i.Name, @Quantity, @TransactionType, i.UnitCost, @Notes, @TransactionDate, @EmployeeId, e.Name
                        FROM InventoryItems i, Employees e
                        WHERE i.Id = @ItemId AND e.Id = @EmployeeId";

                    using var insertCommand = new SqlCommand(insertQuery, connection, transaction);
                    insertCommand.Parameters.AddWithValue("@ItemId", itemId);
                    insertCommand.Parameters.AddWithValue("@Quantity", newStock);
                    insertCommand.Parameters.AddWithValue("@TransactionType", transactionType);
                    insertCommand.Parameters.AddWithValue("@Notes", notes);
                    insertCommand.Parameters.AddWithValue("@TransactionDate", DateTime.Now);
                    insertCommand.Parameters.AddWithValue("@EmployeeId", employeeId);
                    insertCommand.ExecuteNonQuery();

                    transaction.Commit();
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المخزون: {ex.Message}");
            }
        }

        public List<StockTransaction> GetStockTransactions(int branchId = 0, int itemId = 0, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var transactions = new List<StockTransaction>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                var conditions = new List<string>();
                if (branchId > 0) conditions.Add("st.BranchId = @BranchId");
                if (itemId > 0) conditions.Add("st.InventoryItemId = @ItemId");
                if (fromDate.HasValue) conditions.Add("st.TransactionDate >= @FromDate");
                if (toDate.HasValue) conditions.Add("st.TransactionDate <= @ToDate");

                string whereClause = conditions.Count > 0 ? "WHERE " + string.Join(" AND ", conditions) : "";

                string query = $@"SELECT st.Id, st.BranchId, st.InventoryItemId, st.ItemName, st.Quantity,
                    st.TransactionType, st.UnitCost, st.Notes, st.TransactionDate, st.EmployeeId, st.EmployeeName,
                    st.InvoiceNumber, st.TransferToBranchId, st.TransferToBranchName
                    FROM StockTransactions st {whereClause}
                    ORDER BY st.TransactionDate DESC";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0) command.Parameters.AddWithValue("@BranchId", branchId);
                if (itemId > 0) command.Parameters.AddWithValue("@ItemId", itemId);
                if (fromDate.HasValue) command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                if (toDate.HasValue) command.Parameters.AddWithValue("@ToDate", toDate.Value);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    transactions.Add(new StockTransaction
                    {
                        Id = reader.GetInt32(0),
                        BranchId = reader.GetInt32(1),
                        InventoryItemId = reader.GetInt32(2),
                        ItemName = reader.GetString(3),
                        Quantity = reader.GetDecimal(4),
                        TransactionType = reader.GetString(5),
                        UnitCost = reader.GetDecimal(6),
                        Notes = reader.IsDBNull(7) ? "" : reader.GetString(7),
                        TransactionDate = reader.GetDateTime(8),
                        EmployeeId = reader.GetInt32(9),
                        EmployeeName = reader.GetString(10),
                        InvoiceNumber = reader.IsDBNull(11) ? "" : reader.GetString(11),
                        TransferToBranchId = reader.IsDBNull(12) ? null : reader.GetInt32(12),
                        TransferToBranchName = reader.IsDBNull(13) ? "" : reader.GetString(13)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب حركات المخزون: {ex.Message}");
            }

            return transactions;
        }

        public List<string> GetInventoryCategories(int branchId = 0)
        {
            var categories = new List<string>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = branchId > 0
                    ? "SELECT DISTINCT Category FROM InventoryItems WHERE BranchId = @BranchId ORDER BY Category"
                    : "SELECT DISTINCT Category FROM InventoryItems ORDER BY Category";

                using var command = new SqlCommand(query, connection);
                if (branchId > 0)
                    command.Parameters.AddWithValue("@BranchId", branchId);

                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    categories.Add(reader.GetString(0));
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب فئات المخزون: {ex.Message}");
            }

            return categories;
        }
    }
}
