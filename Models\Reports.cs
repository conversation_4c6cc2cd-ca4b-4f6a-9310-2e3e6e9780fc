using System;
using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public class SalesReport
    {
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public DateTime ReportDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalServiceCharge { get; set; }
        public decimal NetSales { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageOrderValue { get; set; }
        public int DineInOrders { get; set; }
        public int TakeawayOrders { get; set; }
        public int DeliveryOrders { get; set; }
        public decimal DineInSales { get; set; }
        public decimal TakeawaySales { get; set; }
        public decimal DeliverySales { get; set; }
    }

    public class ItemSalesReport
    {
        public int MenuItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int QuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal ItemCost { get; set; }
        public decimal Profit { get; set; }
        public decimal ProfitMargin { get; set; }
    }

    public class EmployeePerformance
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int OrdersHandled { get; set; }
        public decimal TotalSales { get; set; }
        public decimal HoursWorked { get; set; }
        public decimal SalesPerHour { get; set; }
        public int CustomerComplaints { get; set; }
        public decimal PerformanceScore { get; set; }
    }

    public class InventoryReport
    {
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int ExpiringSoonItems { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal MonthlyConsumption { get; set; }
        public List<InventoryItem> LowStockList { get; set; } = new List<InventoryItem>();
        public List<InventoryItem> ExpiringSoonList { get; set; } = new List<InventoryItem>();
    }

    public class FinancialSummary
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public Dictionary<string, decimal> RevenueByBranch { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> CostsByCategory { get; set; } = new Dictionary<string, decimal>();
    }
}