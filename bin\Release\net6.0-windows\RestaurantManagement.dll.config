<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <connectionStrings>
        <!-- اتصال LocalDB (الافتراضي) -->
        <add name="RestaurantDB"
             connectionString="Server=(localdb)\MSSQLLocalDB;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;"
             providerName="Microsoft.Data.SqlClient" />

        <!-- اتصال SQL Server المحلي -->
        <!--
        <add name="RestaurantDB"
             connectionString="Server=localhost;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;Charset=utf8;"
             providerName="Microsoft.Data.SqlClient" />
        -->

        <!-- اتصال SQL Server Express -->
        <!--
        <add name="RestaurantDB"
             connectionString="Server=.\SQLEXPRESS;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;Charset=utf8;"
             providerName="Microsoft.Data.SqlClient" />
        -->

        <!-- اتصال SQL Server مع اسم مستخدم وكلمة مرور -->
        <!--
        <add name="RestaurantDB"
             connectionString="Server=localhost;Database=RestaurantManagement;User Id=sa;Password=YourPassword;TrustServerCertificate=true;MultipleActiveResultSets=true;Charset=utf8;"
             providerName="Microsoft.Data.SqlClient" />
        -->
    </connectionStrings>
    
    <appSettings>
        <!-- إعدادات التطبيق -->
        <add key="TaxRate" value="0.14" />
        <add key="DeliveryFee" value="10.00" />
        <add key="RestaurantName" value="مطعم الأصالة" />
        <add key="RestaurantPhone" value="01000000000" />
        <add key="RestaurantAddress" value="شارع الجمهورية، القاهرة" />
    </appSettings>
</configuration>