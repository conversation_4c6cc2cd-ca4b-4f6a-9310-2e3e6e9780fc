# حل مشاكل الترميز العربي - Arabic Encoding Fix

## 🔧 المشاكل التي تم إصلاحها:

### 1. **قاعدة البيانات**
- ✅ تم تعيين `COLLATE Arabic_CI_AS` لقاعدة البيانات
- ✅ استخدام `NVARCHAR` لجميع النصوص العربية
- ✅ استخدام `Parameters` بدلاً من النص المباشر
- ✅ إضافة `N` prefix للنصوص العربية في SQL

### 2. **سلسلة الاتصال**
- ✅ إضافة `MultipleActiveResultSets=true`
- ✅ إضافة `Charset=utf8`
- ✅ تحسين معالجة الأخطاء

### 3. **التطبيق**
- ✅ تعيين `Console.OutputEncoding = UTF8`
- ✅ تعيين الثقافة العربية `ar-EG`
- ✅ تعيين `CultureInfo` للخيوط الحالية والافتراضية

### 4. **ملفات التشغيل**
- ✅ إضافة `chcp 65001` لدعم UTF-8 في Batch
- ✅ إنشاء ملف PowerShell مع دعم أفضل للترميز
- ✅ تحسين رسائل الخطأ والحالة

---

## 🚀 كيفية التشغيل بعد الإصلاح:

### الطريقة الأولى: PowerShell (مُوصى بها)
```powershell
# افتح PowerShell كمدير وشغّل:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\run.ps1
```

### الطريقة الثانية: Batch File
```cmd
# شغّل من Command Prompt:
run.bat
```

### الطريقة الثالثة: مباشرة من .NET CLI
```bash
dotnet restore
dotnet build --configuration Release
dotnet run --configuration Release
```

---

## 🗄️ إعداد قاعدة البيانات:

### 1. **تشغيل ملف الإعداد المحدث:**
```sql
-- الملف محدث ليدعم الترميز العربي
sqlcmd -S localhost -E -i setup_database.sql
```

### 2. **التحقق من الترميز:**
```sql
-- للتحقق من collation قاعدة البيانات
SELECT name, collation_name FROM sys.databases WHERE name = 'RestaurantManagement'

-- يجب أن يظهر: Arabic_CI_AS
```

### 3. **اختبار النصوص العربية:**
```sql
-- اختبار إدراج واسترجاع النص العربي
USE RestaurantManagement
SELECT Name, Address FROM Branches WHERE Id = 1
-- يجب أن يظهر النص العربي بشكل صحيح
```

---

## ⚙️ إعدادات إضافية:

### 1. **SQL Server Management Studio:**
- تأكد من تعيين الخط إلى خط يدعم العربية (مثل Arial Unicode MS)
- في Tools > Options > Environment > Fonts and Colors
- اختر خط يدعم Unicode

### 2. **Visual Studio:**
- File > Advanced Save Options
- اختر "Unicode (UTF-8 with signature) - Codepage 65001"

### 3. **Windows Terminal/Command Prompt:**
```cmd
# تعيين الترميز لـ UTF-8
chcp 65001
```

---

## 🔍 استكشاف الأخطاء:

### مشكلة: النص العربي يظهر كعلامات استفهام (???)
**الحل:**
1. تأكد من تشغيل `setup_database.sql` المحدث
2. احذف قاعدة البيانات القديمة وأعد إنشاءها
3. تأكد من استخدام `NVARCHAR` في جميع الجداول

### مشكلة: خطأ في الاتصال بقاعدة البيانات
**الحل:**
1. تحقق من تشغيل SQL Server
2. تأكد من صحة سلسلة الاتصال في `App.config`
3. تأكد من صلاحيات المستخدم

### مشكلة: التطبيق لا يبدأ
**الحل:**
1. شغّل `dotnet restore` أولاً
2. تأكد من تثبيت .NET 6.0
3. شغّل كمدير إذا لزم الأمر

---

## 📋 قائمة التحقق:

- [ ] تم تشغيل `setup_database.sql` المحدث
- [ ] قاعدة البيانات تستخدم `Arabic_CI_AS` collation
- [ ] النصوص العربية تظهر بشكل صحيح في SSMS
- [ ] التطبيق يبدأ بدون أخطاء
- [ ] واجهة تسجيل الدخول تظهر النصوص العربية
- [ ] يمكن تسجيل الدخول بـ admin/admin123

---

## 🎯 اختبار سريع:

بعد التشغيل، تحقق من:
1. **واجهة تسجيل الدخول**: هل النصوص العربية تظهر بشكل صحيح؟
2. **القوائم**: هل أسماء القوائم باللغة العربية؟
3. **شريط الحالة**: هل يعرض "المستخدم" و "الفرع" بالعربية؟
4. **قاعدة البيانات**: هل البيانات الافتراضية باللغة العربية؟

---

## 💡 نصائح إضافية:

### لتحسين الأداء:
- استخدم `StringBuilder` للنصوص الطويلة
- استخدم `Parameters` دائماً لتجنب SQL Injection
- فعّل Connection Pooling

### للأمان:
- غيّر كلمة مرور المدير الافتراضية
- استخدم تشفير كلمات المرور
- فعّل SSL للاتصالات

### للصيانة:
- اعمل نسخة احتياطية من قاعدة البيانات بانتظام
- راقب أداء الاستعلامات
- نظّف البيانات القديمة دورياً

---

**تم إصلاح جميع مشاكل الترميز العربي! 🎉**

النظام الآن يدعم اللغة العربية بشكل كامل في:
- واجهة المستخدم
- قاعدة البيانات  
- التقارير والرسائل
- ملفات التشغيل