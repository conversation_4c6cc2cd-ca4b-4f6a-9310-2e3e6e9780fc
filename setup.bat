@echo off
chcp 65001 >nul
cls
echo ========================================
echo    Restaurant Management System Setup
echo ========================================
echo.
echo This will help you set up the database.
echo.
echo Steps to follow:
echo 1. Make sure SQL Server is running
echo 2. Open SQL Server Management Studio (SSMS)
echo 3. Run the setup_database.sql file
echo 4. Update connection string if needed
echo.
echo Connection string location:
echo   Data\DatabaseHelper.cs
echo.
echo Default connection string:
echo   Server=localhost;Database=RestaurantDB;Integrated Security=true;TrustServerCertificate=true;
echo.
echo After database setup, run:
echo   build.bat  (to build the application)
echo   run.bat    (to start the application)
echo.
pause