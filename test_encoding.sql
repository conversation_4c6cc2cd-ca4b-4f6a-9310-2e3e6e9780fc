-- اختبار الترميز العربي في قاعدة البيانات
-- Arabic Encoding Test Script

USE RestaurantManagement;
GO

-- اختبار 1: التحقق من collation قاعدة البيانات
PRINT N'=== اختبار الترميز العربي ===';
PRINT N'';

PRINT N'1. فحص collation قاعدة البيانات:';
SELECT 
    name as [اسم قاعدة البيانات], 
    collation_name as [نوع الترميز]
FROM sys.databases 
WHERE name = 'RestaurantManagement';

PRINT N'';
PRINT N'2. فحص البيانات العربية في جدول الفروع:';
SELECT 
    Id as [المعرف],
    Name as [اسم الفرع], 
    Address as [العنوان],
    Manager as [المدير]
FROM Branches;

PRINT N'';
PRINT N'3. فحص البيانات العربية في جدول الموظفين:';
SELECT 
    Id as [المعرف],
    Name as [اسم الموظف],
    Username as [اسم المستخدم],
    CASE Role 
        WHEN 0 THEN N'مدير'
        WHEN 1 THEN N'كاشير' 
        WHEN 2 THEN N'نادل'
        WHEN 3 THEN N'طباخ'
        WHEN 4 THEN N'دليفري'
        ELSE N'غير محدد'
    END as [الدور]
FROM Employees;

PRINT N'';
PRINT N'4. فحص البيانات العربية في قائمة الطعام:';
SELECT TOP 5
    Id as [المعرف],
    Name as [اسم الصنف],
    Description as [الوصف],
    Category as [الفئة],
    Price as [السعر]
FROM MenuItems
ORDER BY Id;

PRINT N'';
PRINT N'5. فحص البيانات العربية في المخزون:';
SELECT TOP 3
    Id as [المعرف],
    Name as [اسم المادة],
    Category as [الفئة],
    Unit as [الوحدة],
    CurrentStock as [المخزون الحالي],
    Supplier as [المورد]
FROM InventoryItems
ORDER BY Id;

PRINT N'';
PRINT N'6. فحص البيانات العربية في الطاولات:';
SELECT TOP 5
    Id as [المعرف],
    Number as [رقم الطاولة],
    Capacity as [السعة],
    Location as [الموقع],
    CASE Status
        WHEN 0 THEN N'متاح'
        WHEN 1 THEN N'مشغول'
        WHEN 2 THEN N'محجوز'
        WHEN 3 THEN N'قيد التنظيف'
        ELSE N'غير محدد'
    END as [الحالة]
FROM Tables
ORDER BY Number;

PRINT N'';
PRINT N'=== انتهى الاختبار ===';
PRINT N'إذا ظهرت النصوص العربية بشكل صحيح، فإن الترميز يعمل بشكل سليم';

GO