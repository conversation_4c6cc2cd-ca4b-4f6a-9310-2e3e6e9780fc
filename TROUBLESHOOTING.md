# دليل حل المشاكل - Troubleshooting Guide

## 🚨 المشاكل الشائعة وحلولها

### 1. **مشكلة الترميز العربي**

#### المشكلة: النص العربي يظهر كعلامات استفهام (???)
```
الأعراض:
- النصوص العربية تظهر كـ ??? في قاعدة البيانات
- واجهة التطبيق تعرض رموز غريبة
- رسائل الخطأ غير مقروءة
```

#### الحل:
```bash
# 1. احذف قاعدة البيانات القديمة
sqlcmd -S localhost -E -Q "DROP DATABASE IF EXISTS RestaurantManagement"

# 2. شغّل ملف الإعداد المحدث
sqlcmd -S localhost -E -i setup_database.sql

# 3. اختبر الترميز
sqlcmd -S localhost -E -i test_encoding.sql
```

---

### 2. **مشكلة الاتصال بقاعدة البيانات**

#### المشكلة: خطأ في الاتصال بـ SQL Server
```
System.Data.SqlClient.SqlException: A network-related or instance-specific error occurred
```

#### الحل:
```bash
# 1. تحقق من تشغيل SQL Server
services.msc
# ابحث عن SQL Server وتأكد أنه يعمل

# 2. تحقق من سلسلة الاتصال في App.config
# للاتصال المحلي:
Server=localhost;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;

# لـ SQL Server Express:
Server=.\SQLEXPRESS;Database=RestaurantManagement;Integrated Security=true;TrustServerCertificate=true;

# 3. اختبر الاتصال
sqlcmd -S localhost -E -Q "SELECT @@VERSION"
```

---

### 3. **مشكلة عدم تشغيل التطبيق**

#### المشكلة: خطأ "Specify a project or solution file"
```
MSBUILD : error MSB1003: Specify a project or solution file. 
The current working directory does not contain a project or solution file.
```

#### الحل:
```bash
# 1. تحقق من وجود ملفات المشروع
check_environment.bat

# 2. إذا كانت الملفات موجودة، استخدم:
dotnet restore RestaurantManagement.csproj
dotnet build RestaurantManagement.csproj --configuration Release
dotnet run --project RestaurantManagement.csproj

# 3. أو استخدم ملفات التشغيل المحدثة:
build.bat
run.bat
```

#### المشكلة: التطبيق لا يبدأ أو يتوقف فجأة
```
الأعراض:
- رسالة خطأ عند بدء التشغيل
- التطبيق يتوقف بدون رسالة
- نافذة سوداء تظهر وتختفي
```

#### الحل:
```bash
# 1. فحص شامل للبيئة
check_environment.bat

# 2. بناء المشروع خطوة بخطوة
build.bat

# 3. تشغيل مع تفاصيل الأخطاء
dotnet run --project RestaurantManagement.csproj --verbosity detailed
```

---

### 4. **مشكلة تسجيل الدخول**

#### المشكلة: لا يمكن تسجيل الدخول بالبيانات الافتراضية
```
الأعراض:
- رسالة "اسم المستخدم أو كلمة المرور غير صحيحة"
- قائمة الفروع فارغة
- خطأ في قاعدة البيانات
```

#### الحل:
```sql
-- 1. تحقق من وجود البيانات الافتراضية
USE RestaurantManagement;
SELECT * FROM Branches;
SELECT * FROM Employees WHERE Username = 'admin';

-- 2. إذا لم توجد، أعد إدراجها
INSERT INTO Branches (Name, Address, Phone, Email, Manager, OpeningTime, ClosingTime, DeliveryArea)
VALUES (N'الفرع الرئيسي', N'شارع الجمهورية، القاهرة', '01000000000', '<EMAIL>', N'أحمد محمد', '08:00:00', '23:00:00', N'القاهرة الجديدة');

DECLARE @BranchId INT = SCOPE_IDENTITY();

INSERT INTO Employees (BranchId, Name, Phone, Email, Role, Salary, HireDate, Username, Password, CanAccessAllBranches)
VALUES (@BranchId, N'مدير النظام', '01000000000', '<EMAIL>', 0, 8000, GETDATE(), 'admin', 'admin123', 1);
```

---

### 5. **مشكلة الأداء البطيء**

#### المشكلة: التطبيق بطيء أو يتجمد
```
الأعراض:
- بطء في تحميل البيانات
- تجمد واجهة المستخدم
- استهلاك عالي للذاكرة
```

#### الحل:
```sql
-- 1. أعد إنشاء الفهارس
USE RestaurantManagement;

-- فهارس الطلبات
CREATE INDEX IX_Orders_BranchId_OrderDate ON Orders (BranchId, OrderDate);
CREATE INDEX IX_Orders_Status ON Orders (Status);

-- فهارس عناصر الطلبات
CREATE INDEX IX_OrderItems_OrderId ON OrderItems (OrderId);

-- فهارس المخزون
CREATE INDEX IX_InventoryItems_BranchId ON InventoryItems (BranchId);

-- فهارس الطاولات
CREATE INDEX IX_Tables_BranchId_Status ON Tables (BranchId, Status);

-- 2. حدّث إحصائيات الجداول
UPDATE STATISTICS Orders;
UPDATE STATISTICS OrderItems;
UPDATE STATISTICS InventoryItems;
UPDATE STATISTICS Tables;
```

---

### 6. **مشكلة الذاكرة**

#### المشكلة: خطأ OutOfMemoryException
```
System.OutOfMemoryException: Insufficient memory to continue the execution
```

#### الحل:
```xml
<!-- أضف إلى App.config -->
<configuration>
  <runtime>
    <gcServer enabled="true"/>
    <gcConcurrent enabled="true"/>
  </runtime>
</configuration>
```

```csharp
// في الكود، تأكد من إغلاق الاتصالات
using var connection = DatabaseHelper.GetConnection();
// استخدم using statements دائماً
```

---

### 7. **مشكلة الصلاحيات**

#### المشكلة: Access Denied أو Unauthorized
```
الأعراض:
- لا يمكن إنشاء قاعدة البيانات
- لا يمكن الكتابة في الملفات
- خطأ في الوصول للمجلدات
```

#### الحل:
```bash
# 1. شغّل Command Prompt كمدير
# Right-click > Run as Administrator

# 2. أو شغّل PowerShell كمدير
# Right-click > Run as Administrator

# 3. تحقق من صلاحيات SQL Server
sqlcmd -S localhost -E -Q "SELECT IS_SRVROLEMEMBER('sysadmin')"
# يجب أن يرجع 1
```

---

## 🔧 أدوات التشخيص

### 1. **اختبار شامل للنظام**
```bash
# شغّل هذا الأمر لاختبار جميع المكونات
.\run.ps1 -Verbose
```

### 2. **اختبار قاعدة البيانات**
```bash
sqlcmd -S localhost -E -i test_encoding.sql
```

### 3. **اختبار الاتصال**
```bash
dotnet run --configuration Debug
```

### 4. **فحص السجلات**
```bash
# ابحث عن ملفات السجل في:
%TEMP%\RestaurantManagement\
```

---

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم:
```bash
# معلومات النظام
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

# إصدار .NET
dotnet --info

# إصدار SQL Server
sqlcmd -S localhost -E -Q "SELECT @@VERSION"

# معلومات التطبيق
dotnet --version
```

### ملفات السجل المهمة:
- `%TEMP%\RestaurantManagement\app.log`
- Windows Event Viewer > Application Logs
- SQL Server Error Logs

---

## ✅ قائمة التحقق السريع

عند مواجهة أي مشكلة، تحقق من:

- [ ] SQL Server يعمل
- [ ] .NET 6.0 مثبت
- [ ] قاعدة البيانات موجودة ومُعدة بشكل صحيح
- [ ] سلسلة الاتصال صحيحة في App.config
- [ ] الصلاحيات كافية (شغّل كمدير إذا لزم)
- [ ] لا توجد برامج مكافحة فيروسات تحجب التطبيق
- [ ] مساحة القرص كافية
- [ ] الذاكرة كافية (4GB على الأقل)

---

**إذا استمرت المشكلة، راجع الملفات التالية:**
- `README.md` - الدليل الشامل
- `ENCODING_FIX.md` - حلول مشاكل الترميز
- `QUICK_START.md` - دليل التشغيل السريع