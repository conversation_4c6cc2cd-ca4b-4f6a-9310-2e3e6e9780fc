using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class InventoryManagementForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private InventoryService inventoryService;
        private List<InventoryItem> allItems;
        private List<InventoryItem> filteredItems;

        // Controls
        private DataGridView inventoryGrid = null!;
        private ComboBox categoryFilter = null!;
        private ComboBox stockStatusFilter = null!;
        private TextBox searchBox = null!;
        private Button addButton = null!;
        private Button editButton = null!;
        private Button deleteButton = null!;
        private Button refreshButton = null!;
        private Button lowStockButton = null!;
        private Button expiringSoonButton = null!;
        private Label statusLabel = null!;
        private Panel filterPanel = null!;
        private Panel buttonPanel = null!;

        public InventoryManagementForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            inventoryService = new InventoryService();
            allItems = new List<InventoryItem>();
            filteredItems = new List<InventoryItem>();
            
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings
            this.Text = "إدارة المخزون";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            // Filter Panel
            filterPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.LightGray,
                Padding = new Padding(10)
            };

            // Search Box
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            searchBox = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 23),
                PlaceholderText = "ابحث عن صنف..."
            };
            searchBox.TextChanged += SearchBox_TextChanged;

            // Category Filter
            var categoryLabel = new Label
            {
                Text = "الفئة:",
                Location = new Point(290, 15),
                Size = new Size(40, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            categoryFilter = new ComboBox
            {
                Location = new Point(340, 12),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryFilter.SelectedIndexChanged += CategoryFilter_SelectedIndexChanged;

            // Stock Status Filter
            var stockLabel = new Label
            {
                Text = "حالة المخزون:",
                Location = new Point(510, 15),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            stockStatusFilter = new ComboBox
            {
                Location = new Point(600, 12),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            stockStatusFilter.Items.AddRange(new[] { "الكل", "مخزون منخفض", "مخزون طبيعي", "منتهي الصلاحية قريباً" });
            stockStatusFilter.SelectedIndex = 0;
            stockStatusFilter.SelectedIndexChanged += StockStatusFilter_SelectedIndexChanged;

            // Quick Action Buttons
            lowStockButton = new Button
            {
                Text = "مخزون منخفض",
                Location = new Point(10, 45),
                Size = new Size(100, 25),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };
            lowStockButton.Click += LowStockButton_Click;

            expiringSoonButton = new Button
            {
                Text = "منتهي الصلاحية",
                Location = new Point(120, 45),
                Size = new Size(100, 25),
                BackColor = Color.Red,
                ForeColor = Color.White
            };
            expiringSoonButton.Click += ExpiringSoonButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(230, 45),
                Size = new Size(80, 25),
                BackColor = Color.Blue,
                ForeColor = Color.White
            };
            refreshButton.Click += RefreshButton_Click;

            filterPanel.Controls.AddRange(new Control[] {
                searchLabel, searchBox, categoryLabel, categoryFilter,
                stockLabel, stockStatusFilter, lowStockButton, expiringSoonButton, refreshButton
            });

            // Button Panel
            buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.LightGray,
                Padding = new Padding(10)
            };

            addButton = new Button
            {
                Text = "إضافة صنف",
                Location = new Point(10, 15),
                Size = new Size(100, 30),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(120, 15),
                Size = new Size(80, 30),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                Enabled = false
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(210, 15),
                Size = new Size(80, 30),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Enabled = false
            };
            deleteButton.Click += DeleteButton_Click;

            statusLabel = new Label
            {
                Text = "جاري التحميل...",
                Location = new Point(400, 20),
                Size = new Size(300, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            buttonPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, statusLabel
            });

            // DataGridView
            inventoryGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            SetupGridColumns();
            inventoryGrid.SelectionChanged += InventoryGrid_SelectionChanged;
            inventoryGrid.CellDoubleClick += InventoryGrid_CellDoubleClick;

            // Add controls to form
            this.Controls.Add(inventoryGrid);
            this.Controls.Add(filterPanel);
            this.Controls.Add(buttonPanel);

            this.ResumeLayout(false);
        }

        private void SetupGridColumns()
        {
            inventoryGrid.Columns.Clear();

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم الصنف",
                DataPropertyName = "Name",
                Width = 150
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "الفئة",
                DataPropertyName = "Category",
                Width = 100
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentStock",
                HeaderText = "المخزون الحالي",
                DataPropertyName = "CurrentStock",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Unit",
                HeaderText = "الوحدة",
                DataPropertyName = "Unit",
                Width = 80
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MinimumStock",
                HeaderText = "الحد الأدنى",
                DataPropertyName = "MinimumStock",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitCost",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitCost",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Supplier",
                HeaderText = "المورد",
                DataPropertyName = "Supplier",
                Width = 120
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ExpiryDate",
                HeaderText = "تاريخ الانتهاء",
                DataPropertyName = "ExpiryDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });

            inventoryGrid.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BranchName",
                HeaderText = "الفرع",
                DataPropertyName = "BranchName",
                Width = 100
            });
        }

        private void LoadData()
        {
            try
            {
                int branchId = currentBranch?.Id ?? 0;
                allItems = inventoryService.GetAllInventoryItems(branchId);
                LoadCategories();
                ApplyFilters();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCategories()
        {
            try
            {
                categoryFilter.Items.Clear();
                categoryFilter.Items.Add("الكل");

                var categories = inventoryService.GetInventoryCategories(currentBranch?.Id ?? 0);
                foreach (var category in categories)
                {
                    categoryFilter.Items.Add(category);
                }

                categoryFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            filteredItems = allItems.ToList();

            // Filter by search text
            if (!string.IsNullOrWhiteSpace(searchBox.Text))
            {
                string searchText = searchBox.Text.ToLower();
                filteredItems = filteredItems.Where(item =>
                    item.Name.ToLower().Contains(searchText) ||
                    item.Description.ToLower().Contains(searchText) ||
                    item.Supplier.ToLower().Contains(searchText)).ToList();
            }

            // Filter by category
            if (categoryFilter.SelectedIndex > 0)
            {
                string selectedCategory = categoryFilter.SelectedItem.ToString();
                filteredItems = filteredItems.Where(item => item.Category == selectedCategory).ToList();
            }

            // Filter by stock status
            switch (stockStatusFilter.SelectedIndex)
            {
                case 1: // مخزون منخفض
                    filteredItems = filteredItems.Where(item => item.IsLowStock).ToList();
                    break;
                case 2: // مخزون طبيعي
                    filteredItems = filteredItems.Where(item => !item.IsLowStock && !item.IsExpiringSoon).ToList();
                    break;
                case 3: // منتهي الصلاحية قريباً
                    filteredItems = filteredItems.Where(item => item.IsExpiringSoon).ToList();
                    break;
            }

            inventoryGrid.DataSource = filteredItems;
            ColorizeRows();
            UpdateStatusLabel();
        }

        private void ColorizeRows()
        {
            foreach (DataGridViewRow row in inventoryGrid.Rows)
            {
                if (row.DataBoundItem is InventoryItem item)
                {
                    if (item.IsExpiringSoon)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightCoral;
                    }
                    else if (item.IsLowStock)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightYellow;
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.White;
                    }
                }
            }
        }

        private void UpdateStatusLabel()
        {
            int totalItems = filteredItems.Count;
            int lowStockCount = filteredItems.Count(item => item.IsLowStock);
            int expiringSoonCount = filteredItems.Count(item => item.IsExpiringSoon);

            statusLabel.Text = $"إجمالي الأصناف: {totalItems} | مخزون منخفض: {lowStockCount} | منتهي الصلاحية قريباً: {expiringSoonCount}";
        }

        // Event Handlers
        private void SearchBox_TextChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryFilter_SelectedIndexChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StockStatusFilter_SelectedIndexChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void LowStockButton_Click(object? sender, EventArgs e)
        {
            stockStatusFilter.SelectedIndex = 1;
        }

        private void ExpiringSoonButton_Click(object? sender, EventArgs e)
        {
            stockStatusFilter.SelectedIndex = 3;
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadData();
        }

        private void InventoryGrid_SelectionChanged(object? sender, EventArgs e)
        {
            bool hasSelection = inventoryGrid.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }

        private void InventoryGrid_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditButton_Click(sender, e);
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            var addForm = new InventoryItemForm(currentEmployee, currentBranch);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            if (inventoryGrid.SelectedRows.Count > 0)
            {
                var selectedItem = (InventoryItem)inventoryGrid.SelectedRows[0].DataBoundItem;
                var editForm = new InventoryItemForm(currentEmployee, currentBranch, selectedItem);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (inventoryGrid.SelectedRows.Count > 0)
            {
                var selectedItem = (InventoryItem)inventoryGrid.SelectedRows[0].DataBoundItem;

                var result = MessageBox.Show(
                    $"هل تريد حذف الصنف '{selectedItem.Name}' من المخزون؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (inventoryService.DeleteInventoryItem(selectedItem.Id))
                        {
                            MessageBox.Show("تم حذف الصنف بنجاح!", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الصنف!", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الصنف: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
    }
}
