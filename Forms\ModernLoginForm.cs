using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class ModernLoginForm : Form
    {
        private BranchService branchService;
        
        // Modern UI Colors
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);
        private readonly Color AccentColor = Color.FromArgb(230, 126, 34);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);
        private readonly Color WhiteColor = Color.White;

        // Controls
        private Panel leftPanel;
        private Panel rightPanel;
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private ComboBox branchComboBox;
        private Button loginButton;
        private Label titleLabel;
        private Label subtitleLabel;
        private Label usernameLabel;
        private Label passwordLabel;
        private Label branchLabel;
        private PictureBox logoBox;

        public Employee? LoggedInEmployee { get; private set; }
        public Branch? SelectedBranch { get; private set; }

        public ModernLoginForm()
        {
            branchService = new BranchService();
            InitializeComponent();
            LoadBranches();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "تسجيل الدخول - نظام إدارة المطعم";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = WhiteColor;
            this.Font = new Font("Segoe UI", 10F);

            // Create main panels
            CreateLeftPanel();
            CreateRightPanel();

            this.Controls.AddRange(new Control[] { leftPanel, rightPanel });
            this.ResumeLayout(false);
        }

        private void CreateLeftPanel()
        {
            leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 400,
                BackColor = PrimaryColor
            };

            // Add gradient background
            leftPanel.Paint += LeftPanel_Paint;

            // Logo
            logoBox = new PictureBox
            {
                Size = new Size(120, 120),
                Location = new Point(140, 100),
                BackColor = Color.Transparent,
                SizeMode = PictureBoxSizeMode.CenterImage
            };

            // Create a simple logo using text
            var logoBitmap = new Bitmap(120, 120);
            using (var g = Graphics.FromImage(logoBitmap))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = SmoothingMode.AntiAlias;
                
                // Draw circle background
                using (var brush = new SolidBrush(Color.FromArgb(100, 255, 255, 255)))
                {
                    g.FillEllipse(brush, 10, 10, 100, 100);
                }
                
                // Draw restaurant icon (simplified)
                using (var pen = new Pen(WhiteColor, 3))
                {
                    g.DrawEllipse(pen, 30, 30, 60, 60);
                    g.DrawLine(pen, 60, 40, 60, 80);
                    g.DrawLine(pen, 45, 55, 75, 55);
                }
            }
            logoBox.Image = logoBitmap;

            // Welcome text
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك",
                Font = new Font("Segoe UI", 24F, FontStyle.Bold),
                ForeColor = WhiteColor,
                Location = new Point(50, 250),
                Size = new Size(300, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var descLabel = new Label
            {
                Text = "نظام إدارة المطاعم الحديث\nسهل الاستخدام وفعال",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.FromArgb(200, 255, 255, 255),
                Location = new Point(50, 300),
                Size = new Size(300, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Features list
            var featuresLabel = new Label
            {
                Text = "✓ إدارة الطلبات والطاولات\n✓ تتبع المخزون الذكي\n✓ تقارير مفصلة\n✓ واجهة عربية حديثة",
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(180, 255, 255, 255),
                Location = new Point(50, 400),
                Size = new Size(300, 100),
                TextAlign = ContentAlignment.MiddleLeft
            };

            leftPanel.Controls.AddRange(new Control[] { 
                logoBox, welcomeLabel, descLabel, featuresLabel 
            });
        }

        private void CreateRightPanel()
        {
            rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = WhiteColor,
                Padding = new Padding(60, 80, 60, 80)
            };

            // Title
            titleLabel = new Label
            {
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 28F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(60, 80),
                Size = new Size(300, 50),
                TextAlign = ContentAlignment.MiddleCenter
            };

            subtitleLabel = new Label
            {
                Text = "أدخل بياناتك للوصول إلى النظام",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.Gray,
                Location = new Point(60, 130),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Branch selection
            branchLabel = new Label
            {
                Text = "الفرع",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(60, 180),
                Size = new Size(100, 25)
            };

            branchComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 12F),
                Location = new Point(60, 210),
                Size = new Size(300, 35),
                DropDownStyle = ComboBoxStyle.DropDownList,
                FlatStyle = FlatStyle.Flat
            };

            // Username
            usernameLabel = new Label
            {
                Text = "اسم المستخدم",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(60, 260),
                Size = new Size(100, 25)
            };

            usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12F),
                Location = new Point(60, 290),
                Size = new Size(300, 35),
                BorderStyle = BorderStyle.FixedSingle,
                Text = "admin" // Default for testing
            };

            // Password
            passwordLabel = new Label
            {
                Text = "كلمة المرور",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = DarkGray,
                Location = new Point(60, 340),
                Size = new Size(100, 25)
            };

            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12F),
                Location = new Point(60, 370),
                Size = new Size(300, 35),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true,
                Text = "admin123" // Default for testing
            };

            // Login button
            loginButton = new Button
            {
                Text = "🔐 دخول",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = WhiteColor,
                BackColor = PrimaryColor,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(60, 430),
                Size = new Size(300, 50),
                Cursor = Cursors.Hand
            };

            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.FlatAppearance.MouseOverBackColor = SecondaryColor;
            loginButton.Click += LoginButton_Click;

            // Add hover effects
            AddHoverEffects();

            // Close button
            var closeButton = new Button
            {
                Text = "✕",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.Gray,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(rightPanel.Width - 40, 10),
                Size = new Size(30, 30),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(50, 255, 0, 0);
            closeButton.Click += (s, e) => this.Close();

            rightPanel.Controls.AddRange(new Control[] { 
                titleLabel, subtitleLabel, branchLabel, branchComboBox,
                usernameLabel, usernameTextBox, passwordLabel, passwordTextBox,
                loginButton, closeButton
            });
        }

        private void AddHoverEffects()
        {
            // TextBox focus effects
            usernameTextBox.Enter += (s, e) => ((TextBox)s).BackColor = Color.FromArgb(250, 250, 255);
            usernameTextBox.Leave += (s, e) => ((TextBox)s).BackColor = WhiteColor;
            
            passwordTextBox.Enter += (s, e) => ((TextBox)s).BackColor = Color.FromArgb(250, 250, 255);
            passwordTextBox.Leave += (s, e) => ((TextBox)s).BackColor = WhiteColor;

            // Button hover effects
            loginButton.MouseEnter += (s, e) => loginButton.BackColor = SecondaryColor;
            loginButton.MouseLeave += (s, e) => loginButton.BackColor = PrimaryColor;
        }

        private void LeftPanel_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background
            var rect = leftPanel.ClientRectangle;
            using (var brush = new LinearGradientBrush(rect, PrimaryColor, SecondaryColor, 45f))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        }

        private void LoadBranches()
        {
            try
            {
                var branches = branchService.GetAllBranches();
                branchComboBox.DataSource = branches;
                branchComboBox.DisplayMember = "Name";
                branchComboBox.ValueMember = "Id";
                
                if (branches.Count > 0)
                    branchComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usernameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    usernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    passwordTextBox.Focus();
                    return;
                }

                if (branchComboBox.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار الفرع", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    branchComboBox.Focus();
                    return;
                }

                // Simple authentication (replace with real authentication)
                if (usernameTextBox.Text == "admin" && passwordTextBox.Text == "admin123")
                {
                    LoggedInEmployee = new Employee
                    {
                        Id = 1,
                        Name = "المدير العام",
                        Username = "admin",
                        Role = EmployeeRole.Manager,
                        BranchId = ((Branch)branchComboBox.SelectedItem).Id
                    };

                    SelectedBranch = (Branch)branchComboBox.SelectedItem;
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
