@echo off
chcp 65001 >nul
cls
echo ========================================
echo        Database Setup - Restaurant Management
echo ========================================
echo.
echo This script will help you set up the database.
echo.
echo Prerequisites:
echo 1. SQL Server must be running
echo 2. SQL Server Management Studio (SSMS) installed
echo.
echo Steps to follow:
echo.
echo 1. Open SQL Server Management Studio (SSMS)
echo 2. Connect to your local SQL Server instance
echo 3. Open and execute: setup_database.sql
echo 4. Open and execute: insert_sample_data.sql
echo.
echo ========================================
echo Files in this directory:
echo ========================================
dir *.sql /b
echo.
echo ========================================
echo After database setup, you can login with:
echo ========================================
echo Username: admin
echo Password: admin123
echo.
echo ========================================
echo Ready to start the application?
echo ========================================
echo.
set /p choice="Do you want to run the application now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting Restaurant Management System...
    echo.
    dotnet run --project RestaurantManagement.csproj
) else (
    echo.
    echo Setup complete! Run 'run.bat' when ready.
)
echo.
pause