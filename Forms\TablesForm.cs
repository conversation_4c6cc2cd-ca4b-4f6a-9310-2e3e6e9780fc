using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class TablesForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private List<Table> tables;
        private System.Windows.Forms.Timer refreshTimer;

        // Controls
        private Panel tablesPanel = null!;
        private ComboBox statusFilterComboBox = null!;
        private Button refreshButton = null!;
        private Button reserveTableButton = null!;
        private Button clearTableButton = null!;
        private Button mergeTablesButton = null!;
        private Button transferTableButton = null!;
        private Button closeButton = null!;
        private Label statusLabel = null!;

        public TablesForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            tables = new List<Table>();
            
            // Auto-refresh timer
            refreshTimer = new System.Windows.Forms.Timer();
            refreshTimer.Interval = 30000; // 30 seconds
            refreshTimer.Tick += (s, e) => LoadTables();
            
            InitializeComponent();
            LoadTables();
            refreshTimer.Start();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "إدارة الطاولات - Tables Management";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            // Header section
            var headerLabel = new Label
            {
                Text = "حالة الطاولات - Tables Status",
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.DarkBlue
            };
            this.Controls.Add(headerLabel);

            // Filter section
            var filterLabel = new Label
            {
                Text = "تصفية:",
                Location = new Point(20, 60),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(filterLabel);

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(80, 60),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "الكل", "متاح", "مشغول", "محجوز", "قيد التنظيف" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += FilterChanged;
            this.Controls.Add(statusFilterComboBox);

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(220, 60),
                Size = new Size(80, 25),
                BackColor = Color.Blue,
                ForeColor = Color.White
            };
            refreshButton.Click += RefreshButton_Click;
            this.Controls.Add(refreshButton);

            // Merge tables button
            mergeTablesButton = new Button
            {
                Text = "دمج طاولات",
                Location = new Point(310, 60),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            mergeTablesButton.Click += MergeTablesButton_Click;
            this.Controls.Add(mergeTablesButton);

            // Transfer table button
            transferTableButton = new Button
            {
                Text = "نقل طاولة",
                Location = new Point(420, 60),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            transferTableButton.Click += TransferTableButton_Click;
            this.Controls.Add(transferTableButton);

            // Status legend
            var legendPanel = new Panel
            {
                Location = new Point(530, 50),
                Size = new Size(420, 40),
                BorderStyle = BorderStyle.FixedSingle
            };

            var legendLabel = new Label
            {
                Text = "الألوان:",
                Location = new Point(5, 10),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            legendPanel.Controls.Add(legendLabel);

            var availableLabel = new Label
            {
                Text = "متاح",
                Location = new Point(60, 10),
                Size = new Size(40, 20),
                BackColor = Color.LightGreen,
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle
            };
            legendPanel.Controls.Add(availableLabel);

            var occupiedLabel = new Label
            {
                Text = "مشغول",
                Location = new Point(110, 10),
                Size = new Size(40, 20),
                BackColor = Color.LightCoral,
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle
            };
            legendPanel.Controls.Add(occupiedLabel);

            var reservedLabel = new Label
            {
                Text = "محجوز",
                Location = new Point(160, 10),
                Size = new Size(40, 20),
                BackColor = Color.LightBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle
            };
            legendPanel.Controls.Add(reservedLabel);

            var cleaningLabel = new Label
            {
                Text = "تنظيف",
                Location = new Point(210, 10),
                Size = new Size(40, 20),
                BackColor = Color.LightYellow,
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle
            };
            legendPanel.Controls.Add(cleaningLabel);

            this.Controls.Add(legendPanel);

            // Tables panel
            tablesPanel = new Panel
            {
                Location = new Point(20, 100),
                Size = new Size(950, 500),
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };
            this.Controls.Add(tablesPanel);

            // Action buttons
            reserveTableButton = new Button
            {
                Text = "حجز طاولة",
                Location = new Point(20, 620),
                Size = new Size(120, 35),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            reserveTableButton.Click += ReserveTableButton_Click;
            this.Controls.Add(reserveTableButton);

            clearTableButton = new Button
            {
                Text = "تحرير طاولة",
                Location = new Point(150, 620),
                Size = new Size(120, 35),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };
            clearTableButton.Click += ClearTableButton_Click;
            this.Controls.Add(clearTableButton);

            closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(850, 620),
                Size = new Size(120, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);

            // Status label
            statusLabel = new Label
            {
                Text = "جاري التحميل...",
                Location = new Point(400, 630),
                Size = new Size(300, 23),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(statusLabel);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void LoadTables()
        {
            try
            {
                // Load tables from database and check for held orders
                tables = LoadTablesFromDatabase();
                ApplyFilters();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطاولات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<Table> LoadTablesFromDatabase()
        {
            var tablesList = new List<Table>();

            try
            {
                var orderService = new OrderService();

                // Create tables 1-25 and check their status
                for (int i = 1; i <= 25; i++)
                {
                    var table = new Table
                    {
                        Id = i,
                        BranchId = currentBranch?.Id ?? 1,
                        Number = i,
                        Capacity = i <= 10 ? 2 : i <= 20 ? 4 : 6,
                        Location = i <= 12 ? "الطابق الأول" : "الطابق الثاني",
                        QRCode = $"TABLE_{currentBranch?.Id ?? 1}_{i}"
                    };

                    // Check if table has held orders (with error handling)
                    try
                    {
                        var heldOrders = orderService.GetHeldOrdersForTable(i);
                        if (heldOrders.Any())
                        {
                            table.Status = TableStatus.Occupied;
                        }
                        else
                        {
                            table.Status = TableStatus.Available;
                        }
                    }
                    catch
                    {
                        // If can't check held orders, assume table is available
                        table.Status = TableStatus.Available;
                    }

                    tablesList.Add(table);
                }
            }
            catch (Exception ex)
            {
                // Fallback to sample tables if database fails
                MessageBox.Show($"خطأ في تحميل الطاولات من قاعدة البيانات، سيتم استخدام البيانات التجريبية: {ex.Message}",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return CreateSampleTables();
            }

            return tablesList;
        }

        private List<Table> CreateSampleTables()
        {
            var sampleTables = new List<Table>();
            var random = new Random();
            
            for (int i = 1; i <= 25; i++)
            {
                var table = new Table
                {
                    Id = i,
                    BranchId = currentBranch?.Id ?? 1,
                    Number = i,
                    Capacity = i <= 10 ? 2 : i <= 20 ? 4 : 6,
                    Status = (TableStatus)random.Next(0, 4), // Random status for demo
                    Location = i <= 12 ? "الطابق الأول" : "الطابق الثاني",
                    QRCode = $"TABLE_{currentBranch?.Id ?? 1}_{i}"
                };

                // Add some reservation details for reserved tables
                if (table.Status == TableStatus.Reserved)
                {
                    table.ReservedBy = "عميل تجريبي";
                    table.ReservationPhone = "01000000000";
                    table.ReservationTime = DateTime.Now.AddHours(1);
                }

                sampleTables.Add(table);
            }
            
            return sampleTables;
        }

        private void ApplyFilters()
        {
            var filteredTables = tables.AsEnumerable();

            // Filter by status
            if (statusFilterComboBox.SelectedIndex > 0)
            {
                var statusFilter = (TableStatus)(statusFilterComboBox.SelectedIndex - 1);
                filteredTables = filteredTables.Where(t => t.Status == statusFilter);
            }

            DisplayTables(filteredTables.ToList());
        }

        private void DisplayTables(List<Table> tablesToDisplay)
        {
            tablesPanel.Controls.Clear();

            int x = 20, y = 20;
            int tableWidth = 120, tableHeight = 100;
            int spacing = 20;
            int tablesPerRow = 7;

            for (int i = 0; i < tablesToDisplay.Count; i++)
            {
                var table = tablesToDisplay[i];
                
                var tableButton = new Button
                {
                    Text = $"طاولة {table.Number}\n{table.Capacity} أشخاص\n{GetStatusText((int)table.Status)}",
                    Location = new Point(x, y),
                    Size = new Size(tableWidth, tableHeight),
                    Tag = table,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 8F, FontStyle.Bold)
                };

                // Set color based on status
                switch (table.Status)
                {
                    case TableStatus.Available:
                        tableButton.BackColor = Color.LightGreen;
                        tableButton.ForeColor = Color.DarkGreen;
                        break;
                    case TableStatus.Occupied:
                        tableButton.BackColor = Color.LightCoral;
                        tableButton.ForeColor = Color.DarkRed;
                        break;
                    case TableStatus.Reserved:
                        tableButton.BackColor = Color.LightBlue;
                        tableButton.ForeColor = Color.DarkBlue;
                        break;
                    case TableStatus.Cleaning:
                        tableButton.BackColor = Color.LightYellow;
                        tableButton.ForeColor = Color.DarkGoldenrod;
                        break;
                }

                tableButton.Click += TableButton_Click;
                tablesPanel.Controls.Add(tableButton);

                // Calculate next position
                x += tableWidth + spacing;
                if ((i + 1) % tablesPerRow == 0)
                {
                    x = 20;
                    y += tableHeight + spacing;
                }
            }
        }

        private string GetStatusText(int status)
        {
            return status switch
            {
                0 => "متاح",
                1 => "مشغول",
                2 => "محجوز",
                3 => "تنظيف",
                _ => "غير محدد"
            };
        }

        private void UpdateStatusLabel()
        {
            int available = tables.Count(t => t.Status == TableStatus.Available);
            int occupied = tables.Count(t => t.Status == TableStatus.Occupied);
            int reserved = tables.Count(t => t.Status == TableStatus.Reserved);
            int cleaning = tables.Count(t => t.Status == TableStatus.Cleaning);

            statusLabel.Text = $"متاح: {available} | مشغول: {occupied} | محجوز: {reserved} | تنظيف: {cleaning}";
        }

        private void TableButton_Click(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Table table)
            {
                ShowTableDetails(table);
            }
        }

        private void ShowTableDetails(Table table)
        {
            string details = $"تفاصيل الطاولة رقم {table.Number}\n\n";
            details += $"السعة: {table.Capacity} أشخاص\n";
            details += $"الموقع: {table.Location}\n";
            details += $"الحالة: {GetStatusText((int)table.Status)}\n";

            if (table.Status == TableStatus.Reserved && !string.IsNullOrEmpty(table.ReservedBy))
            {
                details += $"\nمحجوزة لـ: {table.ReservedBy}\n";
                details += $"الهاتف: {table.ReservationPhone}\n";
                details += $"وقت الحجز: {table.ReservationTime:dd/MM/yyyy HH:mm}\n";
            }

            MessageBox.Show(details, "تفاصيل الطاولة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void FilterChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadTables();
        }

        private void ReserveTableButton_Click(object? sender, EventArgs e)
        {
            // Simple reservation dialog
            var availableTables = tables.Where(t => t.Status == TableStatus.Available).ToList();
            
            if (availableTables.Count == 0)
            {
                MessageBox.Show("لا توجد طاولات متاحة للحجز", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var tableNumbers = string.Join(", ", availableTables.Select(t => t.Number));
            var result = Microsoft.VisualBasic.Interaction.InputBox(
                $"الطاولات المتاحة: {tableNumbers}\n\nأدخل رقم الطاولة للحجز:",
                "حجز طاولة",
                "");

            if (int.TryParse(result, out int tableNumber))
            {
                var table = availableTables.FirstOrDefault(t => t.Number == tableNumber);
                if (table != null)
                {
                    var customerName = Microsoft.VisualBasic.Interaction.InputBox(
                        "أدخل اسم العميل:",
                        "بيانات الحجز",
                        "");

                    if (!string.IsNullOrWhiteSpace(customerName))
                    {
                        var customerPhone = Microsoft.VisualBasic.Interaction.InputBox(
                            "أدخل رقم الهاتف:",
                            "بيانات الحجز",
                            "");

                        // Update table status
                        table.Status = TableStatus.Reserved;
                        table.ReservedBy = customerName.Trim();
                        table.ReservationPhone = customerPhone.Trim();
                        table.ReservationTime = DateTime.Now;

                        MessageBox.Show($"تم حجز الطاولة رقم {tableNumber} بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        ApplyFilters();
                        UpdateStatusLabel();
                    }
                }
                else
                {
                    MessageBox.Show("رقم الطاولة غير صحيح أو غير متاح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ClearTableButton_Click(object? sender, EventArgs e)
        {
            var occupiedTables = tables.Where(t => t.Status == TableStatus.Occupied || t.Status == TableStatus.Reserved).ToList();
            
            if (occupiedTables.Count == 0)
            {
                MessageBox.Show("لا توجد طاولات مشغولة أو محجوزة", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var tableNumbers = string.Join(", ", occupiedTables.Select(t => $"{t.Number}({GetStatusText((int)t.Status)})"));
            var result = Microsoft.VisualBasic.Interaction.InputBox(
                $"الطاولات المشغولة/المحجوزة: {tableNumbers}\n\nأدخل رقم الطاولة لتحريرها:",
                "تحرير طاولة",
                "");

            if (int.TryParse(result, out int tableNumber))
            {
                var table = occupiedTables.FirstOrDefault(t => t.Number == tableNumber);
                if (table != null)
                {
                    var confirmResult = MessageBox.Show(
                        $"هل أنت متأكد من تحرير الطاولة رقم {tableNumber}؟",
                        "تأكيد التحرير",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (confirmResult == DialogResult.Yes)
                    {
                        // Clear table
                        table.Status = TableStatus.Available;
                        table.ReservedBy = "";
                        table.ReservationPhone = "";
                        table.ReservationTime = null;

                        MessageBox.Show($"تم تحرير الطاولة رقم {tableNumber} بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        ApplyFilters();
                        UpdateStatusLabel();
                    }
                }
                else
                {
                    MessageBox.Show("رقم الطاولة غير صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void MergeTablesButton_Click(object? sender, EventArgs e)
        {
            var occupiedTables = tables.Where(t => t.Status == TableStatus.Occupied).ToList();

            if (occupiedTables.Count < 2)
            {
                MessageBox.Show("يجب أن يكون هناك طاولتان مشغولتان على الأقل للدمج", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var mergeForm = new Form
            {
                Text = "دمج الطاولات",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var instructionLabel = new Label
            {
                Text = "اختر الطاولات المراد دمجها:",
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            mergeForm.Controls.Add(instructionLabel);

            var tablesListBox = new CheckedListBox
            {
                Location = new Point(20, 50),
                Size = new Size(340, 150),
                CheckOnClick = true
            };

            foreach (var table in occupiedTables)
            {
                tablesListBox.Items.Add($"طاولة {table.Number}", false);
            }
            mergeForm.Controls.Add(tablesListBox);

            var targetLabel = new Label
            {
                Text = "الطاولة الهدف:",
                Location = new Point(20, 210),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            mergeForm.Controls.Add(targetLabel);

            var targetComboBox = new ComboBox
            {
                Location = new Point(130, 210),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            foreach (var table in occupiedTables)
            {
                targetComboBox.Items.Add($"طاولة {table.Number}");
            }
            mergeForm.Controls.Add(targetComboBox);

            var mergeButton = new Button
            {
                Text = "دمج",
                Location = new Point(250, 210),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White
            };
            mergeButton.Click += (s, args) =>
            {
                var selectedIndices = tablesListBox.CheckedIndices.Cast<int>().ToList();
                if (selectedIndices.Count < 2)
                {
                    MessageBox.Show("يجب اختيار طاولتين على الأقل للدمج", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (targetComboBox.SelectedIndex == -1)
                {
                    MessageBox.Show("يجب اختيار الطاولة الهدف", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var targetTable = occupiedTables[targetComboBox.SelectedIndex];
                var tablesToMerge = selectedIndices.Select(i => occupiedTables[i]).ToList();

                if (!tablesToMerge.Contains(targetTable))
                {
                    MessageBox.Show("الطاولة الهدف يجب أن تكون من ضمن الطاولات المختارة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                MergeTables(tablesToMerge, targetTable);
                mergeForm.Close();
            };
            mergeForm.Controls.Add(mergeButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(280, 250),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            cancelButton.Click += (s, args) => mergeForm.Close();
            mergeForm.Controls.Add(cancelButton);

            mergeForm.ShowDialog();
        }

        private void TransferTableButton_Click(object? sender, EventArgs e)
        {
            var occupiedTables = tables.Where(t => t.Status == TableStatus.Occupied).ToList();
            var availableTables = tables.Where(t => t.Status == TableStatus.Available).ToList();

            if (occupiedTables.Count == 0)
            {
                MessageBox.Show("لا توجد طاولات مشغولة للنقل", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (availableTables.Count == 0)
            {
                MessageBox.Show("لا توجد طاولات متاحة للنقل إليها", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var transferForm = new Form
            {
                Text = "نقل طاولة",
                Size = new Size(350, 200),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var fromLabel = new Label
            {
                Text = "من الطاولة:",
                Location = new Point(20, 30),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            transferForm.Controls.Add(fromLabel);

            var fromComboBox = new ComboBox
            {
                Location = new Point(110, 30),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            foreach (var table in occupiedTables)
            {
                fromComboBox.Items.Add($"طاولة {table.Number}");
            }
            transferForm.Controls.Add(fromComboBox);

            var toLabel = new Label
            {
                Text = "إلى الطاولة:",
                Location = new Point(20, 70),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            transferForm.Controls.Add(toLabel);

            var toComboBox = new ComboBox
            {
                Location = new Point(110, 70),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            foreach (var table in availableTables)
            {
                toComboBox.Items.Add($"طاولة {table.Number}");
            }
            transferForm.Controls.Add(toComboBox);

            var transferButton = new Button
            {
                Text = "نقل",
                Location = new Point(130, 120),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White
            };
            transferButton.Click += (s, args) =>
            {
                if (fromComboBox.SelectedIndex == -1 || toComboBox.SelectedIndex == -1)
                {
                    MessageBox.Show("يجب اختيار الطاولة المصدر والطاولة الهدف", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var fromTable = occupiedTables[fromComboBox.SelectedIndex];
                var toTable = availableTables[toComboBox.SelectedIndex];

                TransferTable(fromTable, toTable);
                transferForm.Close();
            };
            transferForm.Controls.Add(transferButton);

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(220, 120),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            cancelButton.Click += (s, args) => transferForm.Close();
            transferForm.Controls.Add(cancelButton);

            transferForm.ShowDialog();
        }

        private void MergeTables(List<Table> tablesToMerge, Table targetTable)
        {
            try
            {
                var orderService = new OrderService();
                var heldOrders = new Dictionary<int, List<OrderItem>>();

                // Get held orders for all tables to merge
                foreach (var table in tablesToMerge)
                {
                    var orders = orderService.GetHeldOrdersForTable(table.Number);
                    if (orders.Any())
                    {
                        heldOrders[table.Number] = orders;
                    }
                }

                if (!heldOrders.Any())
                {
                    MessageBox.Show("لا توجد طلبات معلقة في الطاولات المختارة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Merge all orders into target table
                var mergedItems = new List<OrderItem>();
                var tableNumbers = new List<int>();

                foreach (var kvp in heldOrders)
                {
                    tableNumbers.Add(kvp.Key);
                    foreach (var item in kvp.Value)
                    {
                        // Check if item already exists in merged list
                        var existingItem = mergedItems.FirstOrDefault(m =>
                            m.MenuItemName == item.MenuItemName &&
                            m.Price == item.Price &&
                            m.DiscountAmount == item.DiscountAmount);

                        if (existingItem != null)
                        {
                            // Combine quantities
                            existingItem.Quantity += item.Quantity;
                        }
                        else
                        {
                            // Add new item
                            mergedItems.Add(new OrderItem
                            {
                                MenuItemName = item.MenuItemName,
                                Price = item.Price,
                                Quantity = item.Quantity,
                                OriginalPrice = item.OriginalPrice,
                                DiscountAmount = item.DiscountAmount
                            });
                        }
                    }
                }

                // Save merged order to target table
                orderService.SaveHeldOrder(targetTable.Number, mergedItems);

                // Clear orders from other tables
                foreach (var tableNumber in tableNumbers)
                {
                    if (tableNumber != targetTable.Number)
                    {
                        orderService.ClearHeldOrder(tableNumber);

                        // Update table status to available
                        var table = tables.FirstOrDefault(t => t.Number == tableNumber);
                        if (table != null)
                        {
                            table.Status = TableStatus.Available;
                        }
                    }
                }

                var tableNumbersText = string.Join("، ", tableNumbers.Where(n => n != targetTable.Number));
                MessageBox.Show(
                    $"تم دمج طلبات الطاولات ({tableNumbersText}) في الطاولة {targetTable.Number} بنجاح\n" +
                    $"إجمالي الأصناف المدمجة: {mergedItems.Count}\n" +
                    $"إجمالي الكمية: {mergedItems.Sum(i => i.Quantity)}",
                    "نجح الدمج",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                LoadTables(); // Refresh display
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في دمج الطاولات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TransferTable(Table fromTable, Table toTable)
        {
            try
            {
                var orderService = new OrderService();
                var heldOrders = orderService.GetHeldOrdersForTable(fromTable.Number);

                if (!heldOrders.Any())
                {
                    MessageBox.Show($"لا توجد طلبات معلقة في الطاولة {fromTable.Number}", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Transfer orders to new table
                orderService.SaveHeldOrder(toTable.Number, heldOrders);
                orderService.ClearHeldOrder(fromTable.Number);

                // Update table statuses
                fromTable.Status = TableStatus.Available;
                toTable.Status = TableStatus.Occupied;

                MessageBox.Show(
                    $"تم نقل طلبات الطاولة {fromTable.Number} إلى الطاولة {toTable.Number} بنجاح\n" +
                    $"عدد الأصناف المنقولة: {heldOrders.Count}\n" +
                    $"إجمالي الكمية: {heldOrders.Sum(i => i.Quantity)}",
                    "نجح النقل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                LoadTables(); // Refresh display
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نقل الطاولة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            refreshTimer?.Stop();
            refreshTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
