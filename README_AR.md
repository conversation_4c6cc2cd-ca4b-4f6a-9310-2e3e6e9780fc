# نظام إدارة المطاعم - Restaurant Management System

## 🚀 كيفية تشغيل التطبيق

### الطريقة الأولى: استخدام الملف الشامل
```cmd
start.bat
```
هذا الملف يوفر قائمة تفاعلية لجميع العمليات.

### الطريقة الثانية: الملفات المنفصلة
```cmd
# 1. بناء التطبيق
build.bat

# 2. تشغيل التطبيق
run.bat

# 3. إعداد قاعدة البيانات (المرة الأولى فقط)
setup.bat
```

### الطريقة الثالثة: الأوامر المباشرة
```cmd
# بناء التطبيق
dotnet build RestaurantManagement.csproj --configuration Release

# تشغيل التطبيق
dotnet run --project RestaurantManagement.csproj
```

## 📋 خطوات الإعداد الأولي

### 1. إعداد قاعدة البيانات
1. تأكد من تشغيل SQL Server
2. افتح SQL Server Management Studio (SSMS)
3. شغّل الملف: `setup_database.sql`
4. تأكد من إنشاء قاعدة البيانات `RestaurantDB`

### 2. تحديث سلسلة الاتصال (إذا لزم الأمر)
- الملف: `Data\DatabaseHelper.cs`
- السلسلة الافتراضية:
```
Server=localhost;Database=RestaurantDB;Integrated Security=true;TrustServerCertificate=true;
```

### 3. بناء وتشغيل التطبيق
```cmd
start.bat
```
ثم اختر الخيار 4 (Build and Run)

## 🔧 حل المشاكل الشائعة

### مشكلة: "Cannot connect to database"
**الحل:**
1. تأكد من تشغيل SQL Server
2. تحقق من سلسلة الاتصال في `DatabaseHelper.cs`
3. تأكد من إنشاء قاعدة البيانات باستخدام `setup_database.sql`

### مشكلة: "Build failed"
**الحل:**
1. تأكد من تثبيت .NET 6.0 SDK
2. شغّل الأمر: `dotnet restore`
3. تحقق من رسائل الخطأ وأصلحها

### مشكلة: عرض النصوص العربية
**الحل:**
- الملفات الجديدة تدعم UTF-8 تلقائياً
- استخدم `start.bat` للحصول على أفضل تجربة

## 📁 هيكل المشروع

```
RestaurantManagement/
├── Data/                 # طبقة البيانات
├── Models/              # النماذج
├── Services/            # الخدمات
├── Forms/               # النوافذ
├── setup_database.sql   # إعداد قاعدة البيانات
├── start.bat           # الملف الشامل
├── build.bat           # بناء التطبيق
├── run.bat             # تشغيل التطبيق
└── setup.bat           # إرشادات الإعداد
```

## 🎯 الميزات

- ✅ نظام تسجيل الدخول
- ✅ إدارة الفروع
- ✅ إدارة الموظفين
- ✅ إدارة القوائم
- ✅ إدارة الطلبات
- ✅ واجهة عربية

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `README_AR.md`
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من متطلبات النظام

---
**تم تطوير النظام بواسطة فريق التطوير**