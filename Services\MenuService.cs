using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Services
{
    public class MenuService
    {
        public List<MenuItem> GetAllMenuItems()
        {
            var items = new List<MenuItem>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT Id, Name, Description, Price, Category, IsAvailable, CreatedDate FROM MenuItems ORDER BY Category, Name";
                using var command = new SqlCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    items.Add(new MenuItem
                    {
                        Id = reader.GetInt32(0), // Id
                        Name = reader.GetString(1), // Name
                        Description = reader.IsDBNull(2) ? "" : reader.GetString(2), // Description
                        Price = reader.GetDecimal(3), // Price
                        Category = reader.GetString(4), // Category
                        IsAvailable = reader.GetBoolean(5), // IsAvailable
                        CreatedDate = reader.GetDateTime(6) // CreatedDate
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetAllMenuItems: {ex.Message}");
            }

            return items;
        }

        public List<string> GetCategories()
        {
            var categories = new List<string>();

            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT DISTINCT Category FROM MenuItems ORDER BY Category";
                using var command = new SqlCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    categories.Add((string)reader["Category"]);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetCategories: {ex.Message}");
            }

            return categories;
        }

        public MenuItem? GetMenuItemById(int id)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "SELECT Id, Name, Description, Price, Category, IsAvailable, CreatedDate FROM MenuItems WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new MenuItem
                    {
                        Id = reader.GetInt32(0), // Id
                        Name = reader.GetString(1), // Name
                        Description = reader.IsDBNull(2) ? "" : reader.GetString(2), // Description
                        Price = reader.GetDecimal(3), // Price
                        Category = reader.GetString(4), // Category
                        IsAvailable = reader.GetBoolean(5), // IsAvailable
                        CreatedDate = reader.GetDateTime(6) // CreatedDate
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في GetMenuItemById: {ex.Message}");
            }

            return null;
        }

        public void AddMenuItem(MenuItem menuItem)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"INSERT INTO MenuItems (Name, Description, Price, Category, IsAvailable, CreatedDate)
                               VALUES (@Name, @Description, @Price, @Category, @IsAvailable, @CreatedDate)";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Name", menuItem.Name);
                command.Parameters.AddWithValue("@Description", menuItem.Description ?? "");
                command.Parameters.AddWithValue("@Price", menuItem.Price);
                command.Parameters.AddWithValue("@Category", menuItem.Category);
                command.Parameters.AddWithValue("@IsAvailable", menuItem.IsAvailable);
                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في AddMenuItem: {ex.Message}");
                throw;
            }
        }

        public void UpdateMenuItem(MenuItem menuItem)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = @"UPDATE MenuItems SET
                               Name = @Name, Description = @Description, Price = @Price,
                               Category = @Category, IsAvailable = @IsAvailable
                               WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", menuItem.Id);
                command.Parameters.AddWithValue("@Name", menuItem.Name);
                command.Parameters.AddWithValue("@Description", menuItem.Description ?? "");
                command.Parameters.AddWithValue("@Price", menuItem.Price);
                command.Parameters.AddWithValue("@Category", menuItem.Category);
                command.Parameters.AddWithValue("@IsAvailable", menuItem.IsAvailable);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateMenuItem: {ex.Message}");
                throw;
            }
        }

        public void DeleteMenuItem(int id)
        {
            try
            {
                using var connection = DatabaseHelper.GetConnection();
                connection.Open();

                string query = "DELETE FROM MenuItems WHERE Id = @Id";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في DeleteMenuItem: {ex.Message}");
                throw;
            }
        }
    }
}