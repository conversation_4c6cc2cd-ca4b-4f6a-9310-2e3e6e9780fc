using System;

namespace RestaurantManagement.Models
{
    public class Branch
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Manager { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime OpeningTime { get; set; }
        public DateTime ClosingTime { get; set; }
        public string DeliveryArea { get; set; } = string.Empty;
        public decimal DeliveryRadius { get; set; } // بالكيلومتر
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public class BranchSettings
    {
        public int Id { get; set; }
        public int BranchId { get; set; }
        public decimal TaxRate { get; set; } = 0.14m;
        public decimal ServiceCharge { get; set; } = 0.10m;
        public decimal DeliveryFee { get; set; } = 15.00m;
        public decimal MinimumOrderForDelivery { get; set; } = 50.00m;
        public bool AcceptOnlineOrders { get; set; } = true;
        public bool AcceptReservations { get; set; } = true;
        public int MaxReservationDays { get; set; } = 30;
    }
}