using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RestaurantManagement.Models;
using RestaurantManagement.Services;

namespace RestaurantManagement.Forms
{
    public partial class OrdersListForm : Form
    {
        private Employee? currentEmployee;
        private Branch? currentBranch;
        private OrderService orderService;
        private List<Order> orders;

        // Controls
        private ListView ordersListView = null!;
        private ComboBox statusFilterComboBox = null!;
        private DateTimePicker fromDatePicker = null!;
        private DateTimePicker toDatePicker = null!;
        private Button refreshButton = null!;
        private Button viewDetailsButton = null!;
        private Button updateStatusButton = null!;
        private Button closeButton = null!;
        private Label totalOrdersLabel = null!;
        private Label totalAmountLabel = null!;

        public OrdersListForm(Employee? employee, Branch? branch)
        {
            currentEmployee = employee;
            currentBranch = branch;
            orderService = new OrderService();
            orders = new List<Order>();
            
            InitializeComponent();
            LoadOrders();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form settings
            this.Text = "عرض الطلبات - Orders List";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F);

            // Filter section
            var filterLabel = new Label
            {
                Text = "تصفية الطلبات:",
                Location = new Point(20, 20),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(filterLabel);

            var statusLabel = new Label
            {
                Text = "الحالة:",
                Location = new Point(20, 50),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(statusLabel);

            statusFilterComboBox = new ComboBox
            {
                Location = new Point(80, 50),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statusFilterComboBox.Items.AddRange(new[] { "الكل", "في الانتظار", "قيد التحضير", "جاهز", "تم التسليم", "ملغي" });
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += FilterChanged;
            this.Controls.Add(statusFilterComboBox);

            var fromLabel = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(220, 50),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(fromLabel);

            fromDatePicker = new DateTimePicker
            {
                Location = new Point(290, 50),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };
            fromDatePicker.ValueChanged += FilterChanged;
            this.Controls.Add(fromDatePicker);

            var toLabel = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(430, 50),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(toLabel);

            toDatePicker = new DateTimePicker
            {
                Location = new Point(500, 50),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };
            toDatePicker.ValueChanged += FilterChanged;
            this.Controls.Add(toDatePicker);

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(640, 50),
                Size = new Size(80, 25),
                BackColor = Color.Blue,
                ForeColor = Color.White
            };
            refreshButton.Click += RefreshButton_Click;
            this.Controls.Add(refreshButton);

            // Orders list
            ordersListView = new ListView
            {
                Location = new Point(20, 90),
                Size = new Size(1150, 450),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false
            };
            ordersListView.Columns.Add("رقم الطلب", 80);
            ordersListView.Columns.Add("العميل", 150);
            ordersListView.Columns.Add("الهاتف", 100);
            ordersListView.Columns.Add("النوع", 100);
            ordersListView.Columns.Add("الحالة", 100);
            ordersListView.Columns.Add("التاريخ", 120);
            ordersListView.Columns.Add("الطاولة", 80);
            ordersListView.Columns.Add("المبلغ", 100);
            ordersListView.Columns.Add("الموظف", 120);
            ordersListView.Columns.Add("ملاحظات", 200);
            this.Controls.Add(ordersListView);

            // Summary section
            var summaryLabel = new Label
            {
                Text = "ملخص:",
                Location = new Point(20, 560),
                Size = new Size(60, 23),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(summaryLabel);

            totalOrdersLabel = new Label
            {
                Text = "عدد الطلبات: 0",
                Location = new Point(100, 560),
                Size = new Size(150, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(totalOrdersLabel);

            totalAmountLabel = new Label
            {
                Text = "إجمالي المبلغ: 0.00 جنيه",
                Location = new Point(270, 560),
                Size = new Size(200, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.Blue
            };
            this.Controls.Add(totalAmountLabel);

            // Action buttons
            viewDetailsButton = new Button
            {
                Text = "عرض التفاصيل",
                Location = new Point(700, 560),
                Size = new Size(120, 35),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            viewDetailsButton.Click += ViewDetailsButton_Click;
            this.Controls.Add(viewDetailsButton);

            updateStatusButton = new Button
            {
                Text = "تحديث الحالة",
                Location = new Point(830, 560),
                Size = new Size(120, 35),
                BackColor = Color.Orange,
                ForeColor = Color.White
            };
            updateStatusButton.Click += UpdateStatusButton_Click;
            this.Controls.Add(updateStatusButton);

            closeButton = new Button
            {
                Text = "إغلاق",
                Location = new Point(960, 560),
                Size = new Size(120, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void LoadOrders()
        {
            try
            {
                orders = orderService.GetOrdersByBranch(currentBranch?.Id ?? 1);
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلبات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredOrders = orders.AsEnumerable();

            // Filter by status
            if (statusFilterComboBox.SelectedIndex > 0)
            {
                var statusFilter = (OrderStatus)(statusFilterComboBox.SelectedIndex - 1);
                filteredOrders = filteredOrders.Where(o => o.Status == statusFilter);
            }

            // Filter by date range
            var fromDate = fromDatePicker.Value.Date;
            var toDate = toDatePicker.Value.Date.AddDays(1);
            filteredOrders = filteredOrders.Where(o => o.OrderDate >= fromDate && o.OrderDate < toDate);

            UpdateOrdersList(filteredOrders.ToList());
        }

        private void UpdateOrdersList(List<Order> filteredOrders)
        {
            ordersListView.Items.Clear();

            foreach (var order in filteredOrders)
            {
                var listItem = new ListViewItem(order.Id.ToString());
                listItem.SubItems.Add(order.CustomerName ?? "");
                listItem.SubItems.Add(order.CustomerPhone ?? "");
                listItem.SubItems.Add(GetOrderTypeText((int)order.Type));
                listItem.SubItems.Add(GetOrderStatusText((int)order.Status));
                listItem.SubItems.Add(order.OrderDate.ToString("dd/MM/yyyy HH:mm"));
                listItem.SubItems.Add(order.TableNumber?.ToString() ?? "-");
                listItem.SubItems.Add(order.TotalAmount.ToString("F2"));
                listItem.SubItems.Add(""); // Employee name - will be loaded from service
                listItem.SubItems.Add(order.Notes ?? "");
                listItem.Tag = order;

                // Color coding based on status
                switch (order.Status)
                {
                    case OrderStatus.Pending:
                        listItem.BackColor = Color.LightYellow;
                        break;
                    case OrderStatus.Preparing:
                        listItem.BackColor = Color.LightBlue;
                        break;
                    case OrderStatus.Ready:
                        listItem.BackColor = Color.LightGreen;
                        break;
                    case OrderStatus.Delivered:
                        listItem.BackColor = Color.LightGray;
                        break;
                    case OrderStatus.Cancelled:
                        listItem.BackColor = Color.LightCoral;
                        break;
                }

                ordersListView.Items.Add(listItem);
            }

            // Update summary
            totalOrdersLabel.Text = $"عدد الطلبات: {filteredOrders.Count}";
            decimal totalAmount = filteredOrders.Sum(o => o.TotalAmount);
            totalAmountLabel.Text = $"إجمالي المبلغ: {totalAmount:F2} جنيه";
        }

        private string GetOrderTypeText(int type)
        {
            return type switch
            {
                0 => "تناول في المطعم",
                1 => "تكاوي",
                2 => "دليفري",
                _ => "غير محدد"
            };
        }

        private string GetOrderStatusText(int status)
        {
            return status switch
            {
                0 => "في الانتظار",
                1 => "قيد التحضير",
                2 => "جاهز",
                3 => "تم التسليم",
                4 => "ملغي",
                _ => "غير محدد"
            };
        }

        private void FilterChanged(object? sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadOrders();
        }

        private void ViewDetailsButton_Click(object? sender, EventArgs e)
        {
            if (ordersListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار طلب لعرض تفاصيله", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedOrder = (Order)ordersListView.SelectedItems[0].Tag;
            
            // This will be implemented when we create OrderDetailsForm
            MessageBox.Show($"تفاصيل الطلب رقم: {selectedOrder.Id}\n" +
                          $"العميل: {selectedOrder.CustomerName}\n" +
                          $"المبلغ: {selectedOrder.TotalAmount:F2} جنيه\n" +
                          $"الحالة: {GetOrderStatusText((int)selectedOrder.Status)}",
                          "تفاصيل الطلب", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UpdateStatusButton_Click(object? sender, EventArgs e)
        {
            if (ordersListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار طلب لتحديث حالته", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedOrder = (Order)ordersListView.SelectedItems[0].Tag;
            
            // Simple status update dialog
            var statusOptions = new[] { "في الانتظار", "قيد التحضير", "جاهز", "تم التسليم", "ملغي" };
            var result = Microsoft.VisualBasic.Interaction.InputBox(
                "اختر الحالة الجديدة:\n0 - في الانتظار\n1 - قيد التحضير\n2 - جاهز\n3 - تم التسليم\n4 - ملغي",
                "تحديث حالة الطلب",
                ((int)selectedOrder.Status).ToString());

            if (int.TryParse(result, out int newStatus) && newStatus >= 0 && newStatus <= 4)
            {
                try
                {
                    orderService.UpdateOrderStatus(selectedOrder.Id, newStatus);
                    MessageBox.Show("تم تحديث حالة الطلب بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadOrders();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث حالة الطلب: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
